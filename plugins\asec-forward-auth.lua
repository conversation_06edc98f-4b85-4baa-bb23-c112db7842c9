--
-- Licensed to the Apache Software Foundation (ASF) under one or more
-- contributor license agreements.  See the NOTICE file distributed with
-- this work for additional information regarding copyright ownership.
-- The ASF licenses this file to You under the Apache License, Version 2.0
-- (the "License"); you may not use this file except in compliance with
-- the License.  You may obtain a copy of the License at
--
--     http://www.apache.org/licenses/LICENSE-2.0
--
-- Unless required by applicable law or agreed to in writing, software
-- distributed under the License is distributed on an "AS IS" BASIS,
-- WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
-- See the License for the specific language governing permissions and
-- limitations under the License.
--

local ipairs = ipairs
local core   = require("apisix.core")
local http   = require("resty.http")
local cjson = require("cjson")
local ngx         = ngx

local schema = {
    type = "object",
    properties = {
        uri = {type = "string"},
        --verify_uri = {type = "string"},
        allow_degradation = {type = "boolean", default = false},
        ssl_verify = {
            type = "boolean",
            default = true,
        },
        request_method = {
            type = "string",
            default = "GET",
            enum = {"GET", "POST"},
            description = "the method for client to request the authorization service"
        },
        request_headers = {
            type = "array",
            default = {},
            items = {type = "string"},
            description = "client request header that will be sent to the authorization service"
        },
        upstream_headers = {
            type = "array",
            default = {},
            items = {type = "string"},
            description = "authorization response header that will be sent to the upstream"
        },
        client_headers = {
            type = "array",
            default = {},
            items = {type = "string"},
            description = "authorization response header that will be sent to"
                           .. "the client when authorizing failed"
        },
        timeout = {
            type = "integer",
            minimum = 1,
            maximum = 60000,
            default = 3000,
            description = "timeout in milliseconds",
        },
        keepalive = {type = "boolean", default = true},
        keepalive_timeout = {type = "integer", minimum = 1000, default = 60000},
        
        keepalive_pool = {type = "integer", minimum = 1, default = 5},        
          micro_app_detection = {
            type = "object",
            properties = {
                office_app_type = {type = "string", enum = {"dingtalk", "qiyewx", "feishu", "zhezhending"}},
                detection_enabled = {type = "boolean", default = false},
                platform_host = {type = "string"}, -- 管理平台地址，如：https://platform.example.com
                corp_id = {type = "string"}, -- 企业ID（钉钉/企微/飞书需要）
                app_id = {type = "string"},  -- 应用ID（钉钉/企微/飞书需要）
            },
            required = {"office_app_type", "platform_host", "corp_id", "app_id"}
        },
    },
    required = {"uri"}
}


local _M = {
    version = 0.1,
    priority = 2003,
    name = "asec-forward-auth",
    schema = schema,
}


function _M.check_schema(conf)
    return core.schema.check(schema, conf)
end

local function set_asec_header(res,ctx)
    core.log.warn("get response header name:'Asec-User-Id', value: ",res.headers['Asec-User-Id'])
    core.request.set_header(ctx, 'Asec_User_Id', res.headers['Asec-User-Id'])
    core.ctx.register_var("Asec-User-Id", function(ctx)
        return res.headers['Asec-User-Id']
    end)

    local data = cjson.decode(res.body)
    core.request.set_header(ctx, 'Asec_User_Name', data['userName'])
    core.ctx.register_var("Asec-User-Name", function(ctx)
        return data['userName']
    end)
    -- 应用二次认证idp列表
    local idp_list;
    if #data['smsIdp'] > 1 then
        idp_list= table.concat(data['smsIdp'])..","
    else
        idp_list = table.concat(data['smsIdp'])..""
    end

    core.request.set_header(ctx, 'Asec_Sms_Idp', idp_list)

    core.log.warn("smsIdp: ", idp_list)
    core.request.set_header(ctx, 'Asec_Group_Id', res.headers['Asec_Group_Id'])
    core.request.set_header(ctx, 'Asec_last_phone_number', res.headers['Asec_last_phone_number'])
    core.request.set_header(ctx, 'Asec_full_phone_number', res.headers['Asec_full_phone_number'])
    core.request.set_header(ctx, 'Asec_full_email', res.headers['Asec_full_email'])
    core.request.set_header(ctx, 'Asec_prefix_email', res.headers['Asec_prefix_email'])
    core.request.set_header(ctx, 'Asec_User_Identifier', res.headers['Asec_User_Identifier'])
end

-- 获取真实的访问端口（考虑反向代理的端口映射）
local function get_real_port(ctx)
    -- 1. 优先从 Host 头部解析端口（用户真实访问端口）
    local host_header = core.request.header(ctx, "Host")
    if host_header and string.find(host_header, ":") then
        local port = string.match(host_header, ":(%d+)$")
        if port then
            return port
        end
    end
    
    -- 2. 从 X-Forwarded-Port 头部获取
    local forwarded_port = core.request.header(ctx, "X-Forwarded-Port")
    if forwarded_port and forwarded_port ~= "" then
        return forwarded_port
    end
    
    -- 3. 如果Host头部没有端口，根据协议返回默认端口
    if host_header then
        local scheme = core.request.get_scheme(ctx)
        if scheme == "https" then
            return "443"
        elseif scheme == "http" then
            return "80"
        end
    end
    
    -- 4. 最后使用 APISIX 内部端口
    return tostring(core.request.get_port(ctx))
end

-- 获取真实的访问主机名（不包含端口）
local function get_real_host(ctx)
    -- 优先从 Host 头部获取（去除端口）
    local host_header = core.request.header(ctx, "Host")
    if host_header then
        if string.find(host_header, ":") then
            return string.match(host_header, "^([^:]+)")
        else
            return host_header
        end
    end
    
    -- 备用方案：使用 APISIX 获取的主机名
    return core.request.get_host(ctx)
end

-- 生成包含JavaScript的HTML重定向页面（用于保留URL fragment）
-- 优化版本：去除样式和动画，减少闪烁，快速跳转
local function generate_js_redirect_html(redirect_url, title, message)
    title = title or "Redirecting..."
    -- 不再使用 message 参数，减少页面元素
    
    return string.format([[<!DOCTYPE html>
<html><head><meta charset="UTF-8"><title>%s</title></head><body><script>
(function(){
var c=window.location.href;
var r='%s';
if(r.indexOf('redirect_url=')===-1){
var s=r.indexOf('?')>-1?'&':'?';
r=r+s+'redirect_url='+encodeURIComponent(c);
}else{
var p=r.split('?');
if(p.length>1){
var u=new URLSearchParams(p[1]);
u.set('redirect_url',c);
r=p[0]+'?'+u.toString();
}else{
r=r+'?redirect_url='+encodeURIComponent(c);
}}
window.location.href=r;
})();
</script></body></html>]], title, redirect_url)
end

-- 构建完整的URL（自动处理标准端口）
local function build_full_url(scheme, host, port, uri)
    local url = scheme .. "://" .. host
    
    -- 只有在非标准端口时才添加端口号
    if not ((scheme == "http" and port == "80") or (scheme == "https" and port == "443")) then
        url = url .. ":" .. port
    end
    
    return url .. uri
end

-- 处理微应用免登录重定向
local function handle_micro_app_redirect(conf, ctx, office_app_type)
    -- 构建当前访问的完整URL，避免重定向循环
    local request_uri = ctx.var.request_uri
    
    -- 检测并清理已经被污染的URL（包含重复的IP地址路径）
    local clean_uri = "/"
    if request_uri and request_uri ~= "/" then
        -- 如果URI包含重复的IP地址段，只保留根路径
        if string.match(request_uri, "/[%d%.]+/[%d%.]+/") then
            core.log.warn("检测到重定向循环污染的URI，重置为根路径: ", request_uri)
            clean_uri = "/"
        else
            clean_uri = request_uri
        end
    end
    
    -- 使用真实的外部访问地址构建URL
    local real_host = get_real_host(ctx)
    local real_port = get_real_port(ctx)
    local scheme = core.request.get_scheme(ctx)
    local current_url = build_full_url(scheme, real_host, real_port, clean_uri)
    
    -- 获取管理平台地址
    local platform_host = conf.micro_app_detection.platform_host or ""
    if platform_host == "" then
        core.log.error("未配置管理平台地址 platform_host")
        return 500
    end
      -- 获取微应用配置信息
    local corp_id = conf.micro_app_detection.corp_id or ""
    local app_id = conf.micro_app_detection.app_id or ""
    
    -- 从完整的认证接口URL中提取基础服务地址
    -- conf.uri 通常是 https://domain.com/auth/login/v1/token_verify
    -- 我们需要提取 https://domain.com 作为基础服务地址供前端使用
    local auth_service_base_url = conf.uri
    
    -- 移除查询参数
    if string.find(auth_service_base_url, "?") then
        auth_service_base_url = string.sub(auth_service_base_url, 1, string.find(auth_service_base_url, "?") - 1)
    end
    
    -- 移除 /auth/login/v1/token_verify 部分，保留基础域名
    if string.find(auth_service_base_url, "/auth/login/v1/") then
        local pos = string.find(auth_service_base_url, "/auth/login/v1/")
        auth_service_base_url = string.sub(auth_service_base_url, 1, pos - 1)
        core.log.warn("从认证接口URL提取基础服务地址: ", conf.uri, " -> ", auth_service_base_url)
    else
        core.log.warn("未找到标准认证路径，使用完整URL: ", auth_service_base_url)
    end
    
    -- 构建重定向到管理平台微应用适配页面的URL
    -- 这个页面将处理微应用的OAuth授权流程，然后回调到认证服务
    local redirect_params = {
        micro_app_type = office_app_type,        -- 微应用类型：dingtalk, qiyewx, feishu
        corp_id = corp_id,                       -- 企业ID（调用SDK需要）
        app_id = app_id,                         -- 应用ID（调用SDK需要）
        redirect_url = current_url,              -- 原始访问地址
        auth_service_url = auth_service_base_url, -- 认证服务基础地址，前端将在此基础上构建具体接口路径
        idp_id = app_id or corp_id               -- IDP标识，优先使用app_id
    }
    
    local encoded_params = ngx.encode_args(redirect_params)
    
    -- 确保platform_host包含协议头，并构建Vue路由hash模式的URL
    local protocol_host = platform_host
    if not string.match(platform_host, "^https?://") then
        protocol_host = "https://" .. platform_host
    end
    
    -- 构建Vue Router hash模式的URL
    local redirect_url = protocol_host .. "/#/login/microapp?" .. encoded_params
      
    -- core.log.warn("微应用重定向配置: ", 
    --               "office_app_type=", office_app_type,
    --               ", corp_id=", corp_id, 
    --               ", app_id=", app_id,
    --               ", 原始auth_url=", conf.uri,
    --               ", 微应用callback_url=", micro_app_callback_url,
    --               " -> 最终重定向=", redirect_url)
    
    -- 检查请求是否来自浏览器，如果是则使用JavaScript重定向保留fragment
    local accept_header = core.request.header(ctx, "Accept")
    if accept_header and string.find(accept_header:lower(), "text/html") then
        -- 使用JavaScript重定向，确保保留URL fragment
        local redirect_html = generate_js_redirect_html(
            redirect_url, 
            "正在跳转到微应用登录...", 
            "正在跳转到微应用登录..."
        )
        
        local client_headers = {
            ["Content-Type"] = "text/html; charset=utf-8",
            ["Cache-Control"] = "no-cache, no-store, must-revalidate",
            ["Pragma"] = "no-cache",
            ["Expires"] = "0"
        }
        core.response.set_header(client_headers)
        
        return 200, redirect_html
    else
        -- 对于非浏览器请求，使用传统302重定向
        core.response.set_header({["Location"] = redirect_url})
        return 302
    end
end

function _M.access(conf, ctx)
    -- 获取端口映射信息
    local internal_host = core.request.get_host(ctx)
    local internal_port = core.request.get_port(ctx)
    local real_host = get_real_host(ctx)
    local real_port = get_real_port(ctx)
    local host_header = core.request.header(ctx, "Host")
    
    core.log.debug("端口映射 - 内部:", internal_host, ":", internal_port, 
                  " -> 外部:", real_host, ":", real_port, 
                  " (Host头:", host_header or "无", ")")

    local cookie = ngx.var.http_cookie
    local request_path = ctx.var.request_uri
    local args = core.request.get_uri_args(ctx)
    local token = nil
    local token_source = ""
    local has_token = false
    
    -- 从Cookie检测token是否存在（仅检查存在性，不进行验证）
    if cookie then
        -- core.log.warn("🔍 检测到Cookie: ", string.sub(cookie, 1, 200), "...")
        token = string.match(cookie, "asec_token=([^;%s]+)")
        if token and token ~= "" and string.len(token) > 10 then
            token_source = "Cookie"
            has_token = true
            -- core.log.warn("✅ 检测到token存在(来源:", token_source, ", 长度:", string.len(token), ")，将进行统一认证校验")
        else
            -- core.log.warn("❌ Cookie中未找到有效的asec_token，cookie内容:", cookie)
            token = nil
        end
    else
        core.log.warn("❌ 请求中没有Cookie")
    end
    
    -- 检查是否是微应用回调请求
    local is_micro_app_callback = args.micro_app_token or args.auth_type == "micro_app"
      -- 微应用User-Agent检测和SSO跳转逻辑（仅当没有token且不是回调请求时进行检测）
    if not has_token and not is_micro_app_callback and conf.micro_app_detection and conf.micro_app_detection.detection_enabled then
        -- core.log.debug("没有token，开始微应用User-Agent检测")        -- 检测微应用环境并进行重定向
        local user_agent = core.request.header(ctx, "User-Agent")
        if user_agent then
            local ua_lower = user_agent:lower()
            local office_app_type = conf.micro_app_detection.office_app_type
            local should_redirect = false
            
            -- 根据办公应用类型检测User-Agent
            if office_app_type == "dingtalk" and string.find(ua_lower, "dingtalk") then
                should_redirect = true
            elseif office_app_type == "qiyewx" and (string.find(ua_lower, "wxwork") or string.find(ua_lower, "micromessenger")) then
                should_redirect = true
            elseif office_app_type == "feishu" and string.find(ua_lower, "lark") then
                should_redirect = true
            elseif office_app_type == "zhezhending" and string.find(ua_lower, "zhezhending") then
                should_redirect = true
            end
            
            if should_redirect then
                -- core.log.debug("检测到", office_app_type, "微应用环境且无token，重定向到管理平台")
                return handle_micro_app_redirect(conf, ctx, office_app_type)
            end
        end
    elseif has_token then
        core.log.debug("已有token，跳过微应用检测，直接进行统一认证校验")
    elseif is_micro_app_callback then
        core.log.debug("微应用回调请求，跳过检测")
    end
    -- 检查URL控制插件处理结果
    local url_control_processed = core.request.header(ctx, 'Asec_URL_Control_Processed')
    if url_control_processed == 'free_auth' then
        core.log.debug("免认证路径，跳过认证")
        return
    elseif url_control_processed == 'forbid_access' then
        core.log.debug("禁止访问路径，跳过认证")
        return
    end
    
    -- 统一的token认证校验（无论有无token都执行一次）
    if has_token then
        core.log.debug("执行统一认证校验（有token）")
    else
        core.log.debug("执行统一认证校验（无token，期望返回重定向）")
    end
    -- 构造认证请求头部，使用真实的外部访问信息
    local real_host = get_real_host(ctx)
    local real_port = get_real_port(ctx)
    local scheme = core.request.get_scheme(ctx)
    
    -- 构建Host头部（标准端口不显示端口号）
    local host_header_value = real_host
    if not ((scheme == "http" and real_port == "80") or (scheme == "https" and real_port == "443")) then
        host_header_value = real_host .. ":" .. real_port
    end
    
    local auth_headers = {
        ["X-Forwarded-Proto"] = scheme,
        ["X-Forwarded-Method"] = core.request.get_method(),
        ["X-Forwarded-Port"] = real_port,                      -- 单独的端口信息
        ["X-Forwarded-Uri"] = ctx.var.request_uri,
        ["X-Forwarded-For"] = core.request.get_remote_client_ip(ctx),
        ["Host"] = host_header_value,                           -- 传递真实的Host头部（标准端口不显示端口号）
        ['Cookie'] = cookie
    }

    -- append headers that need to be get from the client request header
    if #conf.request_headers > 0 then
        for _, header in ipairs(conf.request_headers) do
            if not auth_headers[header] then
                auth_headers[header] = core.request.header(ctx, header)
            end
        end
    end

    -- 使用真实的外部访问地址构建URL
    local scheme = core.request.get_scheme(ctx)
    local full_url = build_full_url(scheme, real_host, real_port, ctx.var.request_uri)
    if string.sub(full_url, -1) == "/" then
        full_url = string.sub(full_url, 1, -2)
    end
    core.log.warn("full url: ",full_url)

    --[[
    local user_agent = core.request.header(ctx, "User-Agent")
    local is_wxwork = false
    if user_agent and string.find(user_agent:lower(), "wxwork") then
        is_wxwork = true
        core.log.warn("wxwork is true")
    end

    local original_scheme = core.request.get_scheme(ctx)
    local original_host = core.request.get_host(ctx)
    local original_port = core.request.get_port(ctx)
    local original_full_url = full_url
    --]]

    local params = {
        headers = auth_headers,
        keepalive = conf.keepalive,
        ssl_verify = conf.ssl_verify,
        method = conf.request_method
    }

    if params.method == "POST" then
        params.body = core.request.get_body()
    end

    if conf.keepalive then
        params.keepalive_timeout = conf.keepalive_timeout
        params.keepalive_pool = conf.keepalive_pool
    end

    local httpc = http.new()
    httpc:set_timeout(conf.timeout)
    local query = {}
    query['redirect_url'] = full_url
    query['host_url'] = conf.uri
    local real_uri = conf.uri
    if next(query) ~= nil then
        local encoded_args = ngx.encode_args(query)
        real_uri = conf.uri .. "?" .. encoded_args
    end
    -- todo 将header设置放到其他插件，并且在用完之后去掉header和url上的code

    core.request.set_header(ctx, 'Asec_Full_Url', full_url)
    core.request.set_header(ctx, 'Asec_Accept-Encoding',core.request.header(ctx, 'Accept-Encoding'))

    local res, err = httpc:request_uri(real_uri, params)
    if not res and conf.allow_degradation then
        return
    elseif not res then
        core.log.error("failed to process forward auth, err: ", err)
        return 403
    end
    
    -- 详细的调试日志 - 查看认证服务的完整响应 这里逐步分析响应内容，提交代码注释掉
    -- core.log.warn("🔍 认证服务完整响应分析:")
    -- core.log.warn("  - 状态码: ", res.status)
    -- core.log.warn("  - 请求中有Cookie: ", (cookie and "是" or "否"))
    -- core.log.warn("  - 响应体长度: ", (res.body and string.len(res.body) or 0))
    -- core.log.warn("  - 响应体前500字符: ", (res.body and string.sub(res.body, 1, 500) or "无"))
    
    -- if res.headers then
    --     core.log.warn("  - 响应头总数: ", (next(res.headers) and "有" or "无"))
    --     for k, v in pairs(res.headers) do
    --         if k:lower() == "set-cookie" or k:lower():find("cookie") then
    --             core.log.warn("  - Cookie相关头 ", k, ": ", v)
    --         elseif k:lower() == "location" then
    --             core.log.warn("  - Location头: ", v)
    --         elseif k:lower():find("asec") then
    --             core.log.warn("  - Asec相关头 ", k, ": ", v)
    --         end
    --     end
        
    --     -- 显示所有响应头（用于调试）
    --     local header_count = 0
    --     for k, v in pairs(res.headers) do
    --         header_count = header_count + 1
    --         if header_count <= 10 then  -- 只显示前10个头
    --             core.log.warn("  - 响应头[", header_count, "] ", k, ": ", (string.len(tostring(v)) > 100 and string.sub(tostring(v), 1, 100) .. "..." or tostring(v)))
    --         end
    --     end
    --     if header_count > 10 then
    --         core.log.warn("  - 还有 ", header_count - 10, " 个响应头...")
    --     end
    -- else
    --     core.log.warn("  - 响应头: 无")
    -- end
    
    if res.status >= 300 then
        -- 认证失败时，如果是微应用环境且配置了微应用检测，尝试重新触发SSO流程
        -- 支持多种失败状态码：302(重定向)、401(未授权)、403(禁止访问)等
        if (res.status == 302 or res.status == 401 or res.status == 403) and 
           conf.micro_app_detection and conf.micro_app_detection.detection_enabled then
            local user_agent = core.request.header(ctx, "User-Agent")
            if user_agent then
                local ua_lower = user_agent:lower()
                local office_app_type = conf.micro_app_detection.office_app_type
                local should_redirect = false
                
                -- 根据办公应用类型检测User-Agent
                if office_app_type == "dingtalk" and string.find(ua_lower, "dingtalk") then
                    should_redirect = true
                    core.log.warn("认证失败(状态码:", res.status, ")，检测到钉钉环境，重定向到微应用SSO")
                elseif office_app_type == "qiyewx" and (string.find(ua_lower, "wxwork") or string.find(ua_lower, "micromessenger")) then
                    should_redirect = true
                    core.log.warn("认证失败(状态码:", res.status, ")，检测到企业微信环境，重定向到微应用SSO")
                elseif office_app_type == "feishu" and string.find(ua_lower, "lark") then
                    should_redirect = true
                    core.log.warn("认证失败(状态码:", res.status, ")，检测到飞书环境，重定向到微应用SSO")
                elseif office_app_type == "zhezhending" and string.find(ua_lower, "zhezhending") then
                    should_redirect = true
                    core.log.warn("认证失败(状态码:", res.status, ")，检测到浙政钉环境，重定向到微应用SSO")
                end
                
                if should_redirect then
                    core.log.debug("认证失败但检测到", office_app_type, "微应用环境，重定向到微应用SSO流程而非普通认证页面")
                    return handle_micro_app_redirect(conf, ctx, office_app_type)
                else
                    core.log.debug("认证失败，但不是", office_app_type, "微应用环境，继续普通认证流程")
                end
            else
                core.log.warn("认证失败，但未检测到User-Agent，继续普通认证流程")
            end
        else
            core.log.debug("认证失败(状态码:", res.status, ")，未配置微应用检测或状态码不匹配，继续普通认证流程")
        end

        --[[
        -- 定义需要匹配的域名和对应的 agent_id 列表
        local auth_domains = {
            ["share.app.asdsec.com:7442"] = "1000011",
            ["oa1.app.asdsec.com:7442"] = "1000009"
        }
        if res.status == 302 and
           auth_domains[original_host .. ":" .. original_port] and
           is_wxwork then

            --构建redirect_uri
            local idp_id = "2d705c8b-2cc8-4b64-90a9-13f3ec7cbb0c"
            local redirect_uri = string.format(
                "https://abcd.asdsec.com:2443/wx_oauth_callback?redirect_url=%s&auth_type=qiyewx&state=%s",
                original_full_url,
                idp_id
            )

            --构建微信授权url
            local app_id = "wwe90dc55454d40d90"
            local agent_id = auth_domains[original_host .. ":" .. original_port]
            local full_url = string.format(
                "https://open.weixin.qq.com/connect/oauth2/authorize" ..
                "?appid=%s" ..
                "&redirect_uri=%s" ..
                "&response_type=code" ..
                "&scope=snsapi_base" ..
                "&agentid=%s" ..
                "&state=%s" ..
                "#wechat_redirect",
                app_id,
                ngx.escape_uri(redirect_uri),
                agent_id,
                idp_id
            )

            --设置Location
            local client_headers2 = {
                ["Location"] = full_url
            }
            core.response.set_header(client_headers2)

            core.log.warn("redirecting to wechat oauth, domain: ", full_url)

            return 302
        end
        --]]

        -- 检查是否为浏览器请求（Accept头包含text/html）
        -- 为了节约时间，这里暂时采用lua内联js的方案，其中正在跳转到认证页面可以去掉
        local accept_header = core.request.header(ctx, "Accept")
        if accept_header and string.find(accept_header:lower(), "text/html") then
            -- 使用认证服务返回的Location头，而不是自己构造登录页面URL
            -- 这样可以保持原有的认证流程（比如 /#/verify 页面的Cookie设置逻辑）
            local redirect_url = res.headers['Location']
            if not redirect_url then
                -- 如果没有Location头，则回退到构造登录页面URL
                local auth_domain = string.match(conf.uri, "^(https?://[^/]+)")
                redirect_url = auth_domain .. "/#/login"
                core.log.warn("认证服务未返回Location头，使用默认登录页面: ", redirect_url)
            else
                core.log.warn("使用认证服务返回的Location头: ", redirect_url)
            end
            
            -- 返回包含JavaScript的HTML页面，使用认证服务指定的重定向URL
            local redirect_html = generate_js_redirect_html(
                redirect_url,
                "正在跳转到认证页面...",
                "正在跳转到认证页面..."
            )
            
            -- 设置响应头
            local client_headers = {
                ["Content-Type"] = "text/html; charset=utf-8",
                ["Cache-Control"] = "no-cache, no-store, must-revalidate",
                ["Pragma"] = "no-cache",
                ["Expires"] = "0"
            }
            core.response.set_header(client_headers)
            
            return 200, redirect_html
        end

        -- 对于非浏览器请求（API请求），使用原来的逻辑返回认证服务的响应
        local client_headers = {
            ["Auth-Verify"] = "1"
        }

        if #conf.client_headers > 0 then
            for _, header in ipairs(conf.client_headers) do
                client_headers[header] = res.headers[header]
            end
        end
        core.response.set_header(client_headers)
        return res.status, res.body
    end

    local response_header = {}
    if res.headers['Set-Cookie'] then
        response_header['Set-Cookie'] =  res.headers['Set-Cookie'] .. "; path=/"
        core.response.set_header(response_header)
    end

    if res.headers['Asec-User-Id'] then
        set_asec_header(res,ctx)
    end


    -- append headers that need to be get from the auth response header
    for _, header in ipairs(conf.upstream_headers) do
        local header_value = res.headers[header]
        if header_value then
            core.request.set_header(ctx, header, header_value)
        end
    end
end


return _M
