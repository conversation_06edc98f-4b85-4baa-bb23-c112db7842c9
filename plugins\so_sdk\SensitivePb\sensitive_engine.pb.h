// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: sensitive_engine.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_sensitive_5fengine_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_sensitive_5fengine_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021012 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_sensitive_5fengine_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_sensitive_5fengine_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_sensitive_5fengine_2eproto;
namespace SensitiveEngine {
class SensitiveDataInfo;
struct SensitiveDataInfoDefaultTypeInternal;
extern SensitiveDataInfoDefaultTypeInternal _SensitiveDataInfo_default_instance_;
class SensitiveElementInfo;
struct SensitiveElementInfoDefaultTypeInternal;
extern SensitiveElementInfoDefaultTypeInternal _SensitiveElementInfo_default_instance_;
class SensitiveExtractPackage;
struct SensitiveExtractPackageDefaultTypeInternal;
extern SensitiveExtractPackageDefaultTypeInternal _SensitiveExtractPackage_default_instance_;
class SensitiveExtractParam;
struct SensitiveExtractParamDefaultTypeInternal;
extern SensitiveExtractParamDefaultTypeInternal _SensitiveExtractParam_default_instance_;
}  // namespace SensitiveEngine
PROTOBUF_NAMESPACE_OPEN
template<> ::SensitiveEngine::SensitiveDataInfo* Arena::CreateMaybeMessage<::SensitiveEngine::SensitiveDataInfo>(Arena*);
template<> ::SensitiveEngine::SensitiveElementInfo* Arena::CreateMaybeMessage<::SensitiveEngine::SensitiveElementInfo>(Arena*);
template<> ::SensitiveEngine::SensitiveExtractPackage* Arena::CreateMaybeMessage<::SensitiveEngine::SensitiveExtractPackage>(Arena*);
template<> ::SensitiveEngine::SensitiveExtractParam* Arena::CreateMaybeMessage<::SensitiveEngine::SensitiveExtractParam>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace SensitiveEngine {

enum SensitiveExtractParam_MatchType : int {
  SensitiveExtractParam_MatchType_kInvalidType = 0,
  SensitiveExtractParam_MatchType_kTrace = 1,
  SensitiveExtractParam_MatchType_kFileType = 2,
  SensitiveExtractParam_MatchType_kFileAttr = 4,
  SensitiveExtractParam_MatchType_kFileName = 8,
  SensitiveExtractParam_MatchType_kFileContent = 16,
  SensitiveExtractParam_MatchType_kSourceProcAndUrl = 32,
  SensitiveExtractParam_MatchType_SensitiveExtractParam_MatchType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  SensitiveExtractParam_MatchType_SensitiveExtractParam_MatchType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool SensitiveExtractParam_MatchType_IsValid(int value);
constexpr SensitiveExtractParam_MatchType SensitiveExtractParam_MatchType_MatchType_MIN = SensitiveExtractParam_MatchType_kInvalidType;
constexpr SensitiveExtractParam_MatchType SensitiveExtractParam_MatchType_MatchType_MAX = SensitiveExtractParam_MatchType_kSourceProcAndUrl;
constexpr int SensitiveExtractParam_MatchType_MatchType_ARRAYSIZE = SensitiveExtractParam_MatchType_MatchType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SensitiveExtractParam_MatchType_descriptor();
template<typename T>
inline const std::string& SensitiveExtractParam_MatchType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, SensitiveExtractParam_MatchType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function SensitiveExtractParam_MatchType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    SensitiveExtractParam_MatchType_descriptor(), enum_t_value);
}
inline bool SensitiveExtractParam_MatchType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, SensitiveExtractParam_MatchType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<SensitiveExtractParam_MatchType>(
    SensitiveExtractParam_MatchType_descriptor(), name, value);
}
enum SensitiveExtractParam_MatchMode : int {
  SensitiveExtractParam_MatchMode_kInvalidMode = 0,
  SensitiveExtractParam_MatchMode_kFilePath = 1,
  SensitiveExtractParam_MatchMode_kFileStream = 2,
  SensitiveExtractParam_MatchMode_SensitiveExtractParam_MatchMode_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  SensitiveExtractParam_MatchMode_SensitiveExtractParam_MatchMode_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool SensitiveExtractParam_MatchMode_IsValid(int value);
constexpr SensitiveExtractParam_MatchMode SensitiveExtractParam_MatchMode_MatchMode_MIN = SensitiveExtractParam_MatchMode_kInvalidMode;
constexpr SensitiveExtractParam_MatchMode SensitiveExtractParam_MatchMode_MatchMode_MAX = SensitiveExtractParam_MatchMode_kFileStream;
constexpr int SensitiveExtractParam_MatchMode_MatchMode_ARRAYSIZE = SensitiveExtractParam_MatchMode_MatchMode_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SensitiveExtractParam_MatchMode_descriptor();
template<typename T>
inline const std::string& SensitiveExtractParam_MatchMode_Name(T enum_t_value) {
  static_assert(::std::is_same<T, SensitiveExtractParam_MatchMode>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function SensitiveExtractParam_MatchMode_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    SensitiveExtractParam_MatchMode_descriptor(), enum_t_value);
}
inline bool SensitiveExtractParam_MatchMode_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, SensitiveExtractParam_MatchMode* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<SensitiveExtractParam_MatchMode>(
    SensitiveExtractParam_MatchMode_descriptor(), name, value);
}
// ===================================================================

class SensitiveExtractParam final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:SensitiveEngine.SensitiveExtractParam) */ {
 public:
  inline SensitiveExtractParam() : SensitiveExtractParam(nullptr) {}
  ~SensitiveExtractParam() override;
  explicit PROTOBUF_CONSTEXPR SensitiveExtractParam(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SensitiveExtractParam(const SensitiveExtractParam& from);
  SensitiveExtractParam(SensitiveExtractParam&& from) noexcept
    : SensitiveExtractParam() {
    *this = ::std::move(from);
  }

  inline SensitiveExtractParam& operator=(const SensitiveExtractParam& from) {
    CopyFrom(from);
    return *this;
  }
  inline SensitiveExtractParam& operator=(SensitiveExtractParam&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SensitiveExtractParam& default_instance() {
    return *internal_default_instance();
  }
  static inline const SensitiveExtractParam* internal_default_instance() {
    return reinterpret_cast<const SensitiveExtractParam*>(
               &_SensitiveExtractParam_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(SensitiveExtractParam& a, SensitiveExtractParam& b) {
    a.Swap(&b);
  }
  inline void Swap(SensitiveExtractParam* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SensitiveExtractParam* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SensitiveExtractParam* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SensitiveExtractParam>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SensitiveExtractParam& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SensitiveExtractParam& from) {
    SensitiveExtractParam::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SensitiveExtractParam* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "SensitiveEngine.SensitiveExtractParam";
  }
  protected:
  explicit SensitiveExtractParam(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef SensitiveExtractParam_MatchType MatchType;
  static constexpr MatchType kInvalidType =
    SensitiveExtractParam_MatchType_kInvalidType;
  static constexpr MatchType kTrace =
    SensitiveExtractParam_MatchType_kTrace;
  static constexpr MatchType kFileType =
    SensitiveExtractParam_MatchType_kFileType;
  static constexpr MatchType kFileAttr =
    SensitiveExtractParam_MatchType_kFileAttr;
  static constexpr MatchType kFileName =
    SensitiveExtractParam_MatchType_kFileName;
  static constexpr MatchType kFileContent =
    SensitiveExtractParam_MatchType_kFileContent;
  static constexpr MatchType kSourceProcAndUrl =
    SensitiveExtractParam_MatchType_kSourceProcAndUrl;
  static inline bool MatchType_IsValid(int value) {
    return SensitiveExtractParam_MatchType_IsValid(value);
  }
  static constexpr MatchType MatchType_MIN =
    SensitiveExtractParam_MatchType_MatchType_MIN;
  static constexpr MatchType MatchType_MAX =
    SensitiveExtractParam_MatchType_MatchType_MAX;
  static constexpr int MatchType_ARRAYSIZE =
    SensitiveExtractParam_MatchType_MatchType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  MatchType_descriptor() {
    return SensitiveExtractParam_MatchType_descriptor();
  }
  template<typename T>
  static inline const std::string& MatchType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, MatchType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function MatchType_Name.");
    return SensitiveExtractParam_MatchType_Name(enum_t_value);
  }
  static inline bool MatchType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      MatchType* value) {
    return SensitiveExtractParam_MatchType_Parse(name, value);
  }

  typedef SensitiveExtractParam_MatchMode MatchMode;
  static constexpr MatchMode kInvalidMode =
    SensitiveExtractParam_MatchMode_kInvalidMode;
  static constexpr MatchMode kFilePath =
    SensitiveExtractParam_MatchMode_kFilePath;
  static constexpr MatchMode kFileStream =
    SensitiveExtractParam_MatchMode_kFileStream;
  static inline bool MatchMode_IsValid(int value) {
    return SensitiveExtractParam_MatchMode_IsValid(value);
  }
  static constexpr MatchMode MatchMode_MIN =
    SensitiveExtractParam_MatchMode_MatchMode_MIN;
  static constexpr MatchMode MatchMode_MAX =
    SensitiveExtractParam_MatchMode_MatchMode_MAX;
  static constexpr int MatchMode_ARRAYSIZE =
    SensitiveExtractParam_MatchMode_MatchMode_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  MatchMode_descriptor() {
    return SensitiveExtractParam_MatchMode_descriptor();
  }
  template<typename T>
  static inline const std::string& MatchMode_Name(T enum_t_value) {
    static_assert(::std::is_same<T, MatchMode>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function MatchMode_Name.");
    return SensitiveExtractParam_MatchMode_Name(enum_t_value);
  }
  static inline bool MatchMode_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      MatchMode* value) {
    return SensitiveExtractParam_MatchMode_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kDlpRuleIdFieldNumber = 1,
    kFilePathFieldNumber = 2,
    kFileStreamFieldNumber = 3,
    kSrcProcFieldNumber = 4,
    kSrcUrlFieldNumber = 5,
    kMatchTypeFieldNumber = 6,
    kMatchModeFieldNumber = 7,
    kUpdateCacheFieldNumber = 8,
  };
  // string dlp_rule_id = 1;
  void clear_dlp_rule_id();
  const std::string& dlp_rule_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_dlp_rule_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_dlp_rule_id();
  PROTOBUF_NODISCARD std::string* release_dlp_rule_id();
  void set_allocated_dlp_rule_id(std::string* dlp_rule_id);
  private:
  const std::string& _internal_dlp_rule_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_dlp_rule_id(const std::string& value);
  std::string* _internal_mutable_dlp_rule_id();
  public:

  // string file_path = 2;
  void clear_file_path();
  const std::string& file_path() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_file_path(ArgT0&& arg0, ArgT... args);
  std::string* mutable_file_path();
  PROTOBUF_NODISCARD std::string* release_file_path();
  void set_allocated_file_path(std::string* file_path);
  private:
  const std::string& _internal_file_path() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_file_path(const std::string& value);
  std::string* _internal_mutable_file_path();
  public:

  // bytes file_stream = 3;
  void clear_file_stream();
  const std::string& file_stream() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_file_stream(ArgT0&& arg0, ArgT... args);
  std::string* mutable_file_stream();
  PROTOBUF_NODISCARD std::string* release_file_stream();
  void set_allocated_file_stream(std::string* file_stream);
  private:
  const std::string& _internal_file_stream() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_file_stream(const std::string& value);
  std::string* _internal_mutable_file_stream();
  public:

  // string src_proc = 4;
  void clear_src_proc();
  const std::string& src_proc() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_src_proc(ArgT0&& arg0, ArgT... args);
  std::string* mutable_src_proc();
  PROTOBUF_NODISCARD std::string* release_src_proc();
  void set_allocated_src_proc(std::string* src_proc);
  private:
  const std::string& _internal_src_proc() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_src_proc(const std::string& value);
  std::string* _internal_mutable_src_proc();
  public:

  // string src_url = 5;
  void clear_src_url();
  const std::string& src_url() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_src_url(ArgT0&& arg0, ArgT... args);
  std::string* mutable_src_url();
  PROTOBUF_NODISCARD std::string* release_src_url();
  void set_allocated_src_url(std::string* src_url);
  private:
  const std::string& _internal_src_url() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_src_url(const std::string& value);
  std::string* _internal_mutable_src_url();
  public:

  // .SensitiveEngine.SensitiveExtractParam.MatchType match_type = 6;
  void clear_match_type();
  ::SensitiveEngine::SensitiveExtractParam_MatchType match_type() const;
  void set_match_type(::SensitiveEngine::SensitiveExtractParam_MatchType value);
  private:
  ::SensitiveEngine::SensitiveExtractParam_MatchType _internal_match_type() const;
  void _internal_set_match_type(::SensitiveEngine::SensitiveExtractParam_MatchType value);
  public:

  // .SensitiveEngine.SensitiveExtractParam.MatchMode match_mode = 7;
  void clear_match_mode();
  ::SensitiveEngine::SensitiveExtractParam_MatchMode match_mode() const;
  void set_match_mode(::SensitiveEngine::SensitiveExtractParam_MatchMode value);
  private:
  ::SensitiveEngine::SensitiveExtractParam_MatchMode _internal_match_mode() const;
  void _internal_set_match_mode(::SensitiveEngine::SensitiveExtractParam_MatchMode value);
  public:

  // bool update_cache = 8;
  void clear_update_cache();
  bool update_cache() const;
  void set_update_cache(bool value);
  private:
  bool _internal_update_cache() const;
  void _internal_set_update_cache(bool value);
  public:

  // @@protoc_insertion_point(class_scope:SensitiveEngine.SensitiveExtractParam)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr dlp_rule_id_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr file_path_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr file_stream_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr src_proc_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr src_url_;
    int match_type_;
    int match_mode_;
    bool update_cache_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_sensitive_5fengine_2eproto;
};
// -------------------------------------------------------------------

class SensitiveElementInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:SensitiveEngine.SensitiveElementInfo) */ {
 public:
  inline SensitiveElementInfo() : SensitiveElementInfo(nullptr) {}
  ~SensitiveElementInfo() override;
  explicit PROTOBUF_CONSTEXPR SensitiveElementInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SensitiveElementInfo(const SensitiveElementInfo& from);
  SensitiveElementInfo(SensitiveElementInfo&& from) noexcept
    : SensitiveElementInfo() {
    *this = ::std::move(from);
  }

  inline SensitiveElementInfo& operator=(const SensitiveElementInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline SensitiveElementInfo& operator=(SensitiveElementInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SensitiveElementInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const SensitiveElementInfo* internal_default_instance() {
    return reinterpret_cast<const SensitiveElementInfo*>(
               &_SensitiveElementInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(SensitiveElementInfo& a, SensitiveElementInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(SensitiveElementInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SensitiveElementInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SensitiveElementInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SensitiveElementInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SensitiveElementInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SensitiveElementInfo& from) {
    SensitiveElementInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SensitiveElementInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "SensitiveEngine.SensitiveElementInfo";
  }
  protected:
  explicit SensitiveElementInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSensitiveElementDataFieldNumber = 3,
    kDescFieldNumber = 2,
    kRuleIdFieldNumber = 1,
  };
  // repeated string sensitive_element_data = 3;
  int sensitive_element_data_size() const;
  private:
  int _internal_sensitive_element_data_size() const;
  public:
  void clear_sensitive_element_data();
  const std::string& sensitive_element_data(int index) const;
  std::string* mutable_sensitive_element_data(int index);
  void set_sensitive_element_data(int index, const std::string& value);
  void set_sensitive_element_data(int index, std::string&& value);
  void set_sensitive_element_data(int index, const char* value);
  void set_sensitive_element_data(int index, const char* value, size_t size);
  std::string* add_sensitive_element_data();
  void add_sensitive_element_data(const std::string& value);
  void add_sensitive_element_data(std::string&& value);
  void add_sensitive_element_data(const char* value);
  void add_sensitive_element_data(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& sensitive_element_data() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_sensitive_element_data();
  private:
  const std::string& _internal_sensitive_element_data(int index) const;
  std::string* _internal_add_sensitive_element_data();
  public:

  // string desc = 2;
  void clear_desc();
  const std::string& desc() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_desc(ArgT0&& arg0, ArgT... args);
  std::string* mutable_desc();
  PROTOBUF_NODISCARD std::string* release_desc();
  void set_allocated_desc(std::string* desc);
  private:
  const std::string& _internal_desc() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_desc(const std::string& value);
  std::string* _internal_mutable_desc();
  public:

  // int32 rule_id = 1;
  void clear_rule_id();
  int32_t rule_id() const;
  void set_rule_id(int32_t value);
  private:
  int32_t _internal_rule_id() const;
  void _internal_set_rule_id(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:SensitiveEngine.SensitiveElementInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> sensitive_element_data_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr desc_;
    int32_t rule_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_sensitive_5fengine_2eproto;
};
// -------------------------------------------------------------------

class SensitiveDataInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:SensitiveEngine.SensitiveDataInfo) */ {
 public:
  inline SensitiveDataInfo() : SensitiveDataInfo(nullptr) {}
  ~SensitiveDataInfo() override;
  explicit PROTOBUF_CONSTEXPR SensitiveDataInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SensitiveDataInfo(const SensitiveDataInfo& from);
  SensitiveDataInfo(SensitiveDataInfo&& from) noexcept
    : SensitiveDataInfo() {
    *this = ::std::move(from);
  }

  inline SensitiveDataInfo& operator=(const SensitiveDataInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline SensitiveDataInfo& operator=(SensitiveDataInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SensitiveDataInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const SensitiveDataInfo* internal_default_instance() {
    return reinterpret_cast<const SensitiveDataInfo*>(
               &_SensitiveDataInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(SensitiveDataInfo& a, SensitiveDataInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(SensitiveDataInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SensitiveDataInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SensitiveDataInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SensitiveDataInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SensitiveDataInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SensitiveDataInfo& from) {
    SensitiveDataInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SensitiveDataInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "SensitiveEngine.SensitiveDataInfo";
  }
  protected:
  explicit SensitiveDataInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSensitiveSourceIdFieldNumber = 6,
    kSrcPathsFieldNumber = 7,
    kFileNameResultFieldNumber = 8,
    kFileContentResultFieldNumber = 9,
    kFileTextResultFieldNumber = 10,
    kSensitiveDataIdFieldNumber = 2,
    kSensitiveDataNameFieldNumber = 3,
    kSensitiveDataCategoryIdFieldNumber = 5,
    kSensitiveDataLevelFieldNumber = 4,
    kHitFieldNumber = 1,
    kFileTypeSuffixFieldNumber = 11,
  };
  // repeated string sensitive_source_id = 6;
  int sensitive_source_id_size() const;
  private:
  int _internal_sensitive_source_id_size() const;
  public:
  void clear_sensitive_source_id();
  const std::string& sensitive_source_id(int index) const;
  std::string* mutable_sensitive_source_id(int index);
  void set_sensitive_source_id(int index, const std::string& value);
  void set_sensitive_source_id(int index, std::string&& value);
  void set_sensitive_source_id(int index, const char* value);
  void set_sensitive_source_id(int index, const char* value, size_t size);
  std::string* add_sensitive_source_id();
  void add_sensitive_source_id(const std::string& value);
  void add_sensitive_source_id(std::string&& value);
  void add_sensitive_source_id(const char* value);
  void add_sensitive_source_id(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& sensitive_source_id() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_sensitive_source_id();
  private:
  const std::string& _internal_sensitive_source_id(int index) const;
  std::string* _internal_add_sensitive_source_id();
  public:

  // repeated string src_paths = 7;
  int src_paths_size() const;
  private:
  int _internal_src_paths_size() const;
  public:
  void clear_src_paths();
  const std::string& src_paths(int index) const;
  std::string* mutable_src_paths(int index);
  void set_src_paths(int index, const std::string& value);
  void set_src_paths(int index, std::string&& value);
  void set_src_paths(int index, const char* value);
  void set_src_paths(int index, const char* value, size_t size);
  std::string* add_src_paths();
  void add_src_paths(const std::string& value);
  void add_src_paths(std::string&& value);
  void add_src_paths(const char* value);
  void add_src_paths(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& src_paths() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_src_paths();
  private:
  const std::string& _internal_src_paths(int index) const;
  std::string* _internal_add_src_paths();
  public:

  // repeated .SensitiveEngine.SensitiveElementInfo file_name_result = 8;
  int file_name_result_size() const;
  private:
  int _internal_file_name_result_size() const;
  public:
  void clear_file_name_result();
  ::SensitiveEngine::SensitiveElementInfo* mutable_file_name_result(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::SensitiveEngine::SensitiveElementInfo >*
      mutable_file_name_result();
  private:
  const ::SensitiveEngine::SensitiveElementInfo& _internal_file_name_result(int index) const;
  ::SensitiveEngine::SensitiveElementInfo* _internal_add_file_name_result();
  public:
  const ::SensitiveEngine::SensitiveElementInfo& file_name_result(int index) const;
  ::SensitiveEngine::SensitiveElementInfo* add_file_name_result();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::SensitiveEngine::SensitiveElementInfo >&
      file_name_result() const;

  // repeated .SensitiveEngine.SensitiveElementInfo file_content_result = 9;
  int file_content_result_size() const;
  private:
  int _internal_file_content_result_size() const;
  public:
  void clear_file_content_result();
  ::SensitiveEngine::SensitiveElementInfo* mutable_file_content_result(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::SensitiveEngine::SensitiveElementInfo >*
      mutable_file_content_result();
  private:
  const ::SensitiveEngine::SensitiveElementInfo& _internal_file_content_result(int index) const;
  ::SensitiveEngine::SensitiveElementInfo* _internal_add_file_content_result();
  public:
  const ::SensitiveEngine::SensitiveElementInfo& file_content_result(int index) const;
  ::SensitiveEngine::SensitiveElementInfo* add_file_content_result();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::SensitiveEngine::SensitiveElementInfo >&
      file_content_result() const;

  // repeated .SensitiveEngine.SensitiveElementInfo file_text_result = 10;
  int file_text_result_size() const;
  private:
  int _internal_file_text_result_size() const;
  public:
  void clear_file_text_result();
  ::SensitiveEngine::SensitiveElementInfo* mutable_file_text_result(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::SensitiveEngine::SensitiveElementInfo >*
      mutable_file_text_result();
  private:
  const ::SensitiveEngine::SensitiveElementInfo& _internal_file_text_result(int index) const;
  ::SensitiveEngine::SensitiveElementInfo* _internal_add_file_text_result();
  public:
  const ::SensitiveEngine::SensitiveElementInfo& file_text_result(int index) const;
  ::SensitiveEngine::SensitiveElementInfo* add_file_text_result();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::SensitiveEngine::SensitiveElementInfo >&
      file_text_result() const;

  // string sensitive_data_id = 2;
  void clear_sensitive_data_id();
  const std::string& sensitive_data_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_sensitive_data_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_sensitive_data_id();
  PROTOBUF_NODISCARD std::string* release_sensitive_data_id();
  void set_allocated_sensitive_data_id(std::string* sensitive_data_id);
  private:
  const std::string& _internal_sensitive_data_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_sensitive_data_id(const std::string& value);
  std::string* _internal_mutable_sensitive_data_id();
  public:

  // string sensitive_data_name = 3;
  void clear_sensitive_data_name();
  const std::string& sensitive_data_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_sensitive_data_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_sensitive_data_name();
  PROTOBUF_NODISCARD std::string* release_sensitive_data_name();
  void set_allocated_sensitive_data_name(std::string* sensitive_data_name);
  private:
  const std::string& _internal_sensitive_data_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_sensitive_data_name(const std::string& value);
  std::string* _internal_mutable_sensitive_data_name();
  public:

  // string sensitive_data_category_id = 5;
  void clear_sensitive_data_category_id();
  const std::string& sensitive_data_category_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_sensitive_data_category_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_sensitive_data_category_id();
  PROTOBUF_NODISCARD std::string* release_sensitive_data_category_id();
  void set_allocated_sensitive_data_category_id(std::string* sensitive_data_category_id);
  private:
  const std::string& _internal_sensitive_data_category_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_sensitive_data_category_id(const std::string& value);
  std::string* _internal_mutable_sensitive_data_category_id();
  public:

  // int32 sensitive_data_level = 4;
  void clear_sensitive_data_level();
  int32_t sensitive_data_level() const;
  void set_sensitive_data_level(int32_t value);
  private:
  int32_t _internal_sensitive_data_level() const;
  void _internal_set_sensitive_data_level(int32_t value);
  public:

  // bool hit = 1;
  void clear_hit();
  bool hit() const;
  void set_hit(bool value);
  private:
  bool _internal_hit() const;
  void _internal_set_hit(bool value);
  public:

  // bool file_type_suffix = 11;
  void clear_file_type_suffix();
  bool file_type_suffix() const;
  void set_file_type_suffix(bool value);
  private:
  bool _internal_file_type_suffix() const;
  void _internal_set_file_type_suffix(bool value);
  public:

  // @@protoc_insertion_point(class_scope:SensitiveEngine.SensitiveDataInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> sensitive_source_id_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> src_paths_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::SensitiveEngine::SensitiveElementInfo > file_name_result_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::SensitiveEngine::SensitiveElementInfo > file_content_result_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::SensitiveEngine::SensitiveElementInfo > file_text_result_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr sensitive_data_id_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr sensitive_data_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr sensitive_data_category_id_;
    int32_t sensitive_data_level_;
    bool hit_;
    bool file_type_suffix_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_sensitive_5fengine_2eproto;
};
// -------------------------------------------------------------------

class SensitiveExtractPackage final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:SensitiveEngine.SensitiveExtractPackage) */ {
 public:
  inline SensitiveExtractPackage() : SensitiveExtractPackage(nullptr) {}
  ~SensitiveExtractPackage() override;
  explicit PROTOBUF_CONSTEXPR SensitiveExtractPackage(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SensitiveExtractPackage(const SensitiveExtractPackage& from);
  SensitiveExtractPackage(SensitiveExtractPackage&& from) noexcept
    : SensitiveExtractPackage() {
    *this = ::std::move(from);
  }

  inline SensitiveExtractPackage& operator=(const SensitiveExtractPackage& from) {
    CopyFrom(from);
    return *this;
  }
  inline SensitiveExtractPackage& operator=(SensitiveExtractPackage&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SensitiveExtractPackage& default_instance() {
    return *internal_default_instance();
  }
  static inline const SensitiveExtractPackage* internal_default_instance() {
    return reinterpret_cast<const SensitiveExtractPackage*>(
               &_SensitiveExtractPackage_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(SensitiveExtractPackage& a, SensitiveExtractPackage& b) {
    a.Swap(&b);
  }
  inline void Swap(SensitiveExtractPackage* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SensitiveExtractPackage* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SensitiveExtractPackage* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SensitiveExtractPackage>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SensitiveExtractPackage& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SensitiveExtractPackage& from) {
    SensitiveExtractPackage::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SensitiveExtractPackage* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "SensitiveEngine.SensitiveExtractPackage";
  }
  protected:
  explicit SensitiveExtractPackage(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTraceIdFieldNumber = 1,
    kSubTraceIdFieldNumber = 2,
    kSensitiveDataInfosFieldNumber = 5,
    kExtensionNameFieldNumber = 4,
    kCategoryIdFieldNumber = 3,
    kFileTypeSuffixFieldNumber = 6,
  };
  // repeated string trace_id = 1;
  int trace_id_size() const;
  private:
  int _internal_trace_id_size() const;
  public:
  void clear_trace_id();
  const std::string& trace_id(int index) const;
  std::string* mutable_trace_id(int index);
  void set_trace_id(int index, const std::string& value);
  void set_trace_id(int index, std::string&& value);
  void set_trace_id(int index, const char* value);
  void set_trace_id(int index, const char* value, size_t size);
  std::string* add_trace_id();
  void add_trace_id(const std::string& value);
  void add_trace_id(std::string&& value);
  void add_trace_id(const char* value);
  void add_trace_id(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& trace_id() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_trace_id();
  private:
  const std::string& _internal_trace_id(int index) const;
  std::string* _internal_add_trace_id();
  public:

  // repeated string sub_trace_id = 2;
  int sub_trace_id_size() const;
  private:
  int _internal_sub_trace_id_size() const;
  public:
  void clear_sub_trace_id();
  const std::string& sub_trace_id(int index) const;
  std::string* mutable_sub_trace_id(int index);
  void set_sub_trace_id(int index, const std::string& value);
  void set_sub_trace_id(int index, std::string&& value);
  void set_sub_trace_id(int index, const char* value);
  void set_sub_trace_id(int index, const char* value, size_t size);
  std::string* add_sub_trace_id();
  void add_sub_trace_id(const std::string& value);
  void add_sub_trace_id(std::string&& value);
  void add_sub_trace_id(const char* value);
  void add_sub_trace_id(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& sub_trace_id() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_sub_trace_id();
  private:
  const std::string& _internal_sub_trace_id(int index) const;
  std::string* _internal_add_sub_trace_id();
  public:

  // repeated .SensitiveEngine.SensitiveDataInfo sensitive_data_infos = 5;
  int sensitive_data_infos_size() const;
  private:
  int _internal_sensitive_data_infos_size() const;
  public:
  void clear_sensitive_data_infos();
  ::SensitiveEngine::SensitiveDataInfo* mutable_sensitive_data_infos(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::SensitiveEngine::SensitiveDataInfo >*
      mutable_sensitive_data_infos();
  private:
  const ::SensitiveEngine::SensitiveDataInfo& _internal_sensitive_data_infos(int index) const;
  ::SensitiveEngine::SensitiveDataInfo* _internal_add_sensitive_data_infos();
  public:
  const ::SensitiveEngine::SensitiveDataInfo& sensitive_data_infos(int index) const;
  ::SensitiveEngine::SensitiveDataInfo* add_sensitive_data_infos();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::SensitiveEngine::SensitiveDataInfo >&
      sensitive_data_infos() const;

  // string extension_name = 4;
  void clear_extension_name();
  const std::string& extension_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_extension_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_extension_name();
  PROTOBUF_NODISCARD std::string* release_extension_name();
  void set_allocated_extension_name(std::string* extension_name);
  private:
  const std::string& _internal_extension_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_extension_name(const std::string& value);
  std::string* _internal_mutable_extension_name();
  public:

  // int32 category_id = 3;
  void clear_category_id();
  int32_t category_id() const;
  void set_category_id(int32_t value);
  private:
  int32_t _internal_category_id() const;
  void _internal_set_category_id(int32_t value);
  public:

  // bool file_type_suffix = 6;
  void clear_file_type_suffix();
  bool file_type_suffix() const;
  void set_file_type_suffix(bool value);
  private:
  bool _internal_file_type_suffix() const;
  void _internal_set_file_type_suffix(bool value);
  public:

  // @@protoc_insertion_point(class_scope:SensitiveEngine.SensitiveExtractPackage)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> trace_id_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> sub_trace_id_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::SensitiveEngine::SensitiveDataInfo > sensitive_data_infos_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr extension_name_;
    int32_t category_id_;
    bool file_type_suffix_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_sensitive_5fengine_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// SensitiveExtractParam

// string dlp_rule_id = 1;
inline void SensitiveExtractParam::clear_dlp_rule_id() {
  _impl_.dlp_rule_id_.ClearToEmpty();
}
inline const std::string& SensitiveExtractParam::dlp_rule_id() const {
  // @@protoc_insertion_point(field_get:SensitiveEngine.SensitiveExtractParam.dlp_rule_id)
  return _internal_dlp_rule_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SensitiveExtractParam::set_dlp_rule_id(ArgT0&& arg0, ArgT... args) {
 
 _impl_.dlp_rule_id_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:SensitiveEngine.SensitiveExtractParam.dlp_rule_id)
}
inline std::string* SensitiveExtractParam::mutable_dlp_rule_id() {
  std::string* _s = _internal_mutable_dlp_rule_id();
  // @@protoc_insertion_point(field_mutable:SensitiveEngine.SensitiveExtractParam.dlp_rule_id)
  return _s;
}
inline const std::string& SensitiveExtractParam::_internal_dlp_rule_id() const {
  return _impl_.dlp_rule_id_.Get();
}
inline void SensitiveExtractParam::_internal_set_dlp_rule_id(const std::string& value) {
  
  _impl_.dlp_rule_id_.Set(value, GetArenaForAllocation());
}
inline std::string* SensitiveExtractParam::_internal_mutable_dlp_rule_id() {
  
  return _impl_.dlp_rule_id_.Mutable(GetArenaForAllocation());
}
inline std::string* SensitiveExtractParam::release_dlp_rule_id() {
  // @@protoc_insertion_point(field_release:SensitiveEngine.SensitiveExtractParam.dlp_rule_id)
  return _impl_.dlp_rule_id_.Release();
}
inline void SensitiveExtractParam::set_allocated_dlp_rule_id(std::string* dlp_rule_id) {
  if (dlp_rule_id != nullptr) {
    
  } else {
    
  }
  _impl_.dlp_rule_id_.SetAllocated(dlp_rule_id, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.dlp_rule_id_.IsDefault()) {
    _impl_.dlp_rule_id_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:SensitiveEngine.SensitiveExtractParam.dlp_rule_id)
}

// string file_path = 2;
inline void SensitiveExtractParam::clear_file_path() {
  _impl_.file_path_.ClearToEmpty();
}
inline const std::string& SensitiveExtractParam::file_path() const {
  // @@protoc_insertion_point(field_get:SensitiveEngine.SensitiveExtractParam.file_path)
  return _internal_file_path();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SensitiveExtractParam::set_file_path(ArgT0&& arg0, ArgT... args) {
 
 _impl_.file_path_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:SensitiveEngine.SensitiveExtractParam.file_path)
}
inline std::string* SensitiveExtractParam::mutable_file_path() {
  std::string* _s = _internal_mutable_file_path();
  // @@protoc_insertion_point(field_mutable:SensitiveEngine.SensitiveExtractParam.file_path)
  return _s;
}
inline const std::string& SensitiveExtractParam::_internal_file_path() const {
  return _impl_.file_path_.Get();
}
inline void SensitiveExtractParam::_internal_set_file_path(const std::string& value) {
  
  _impl_.file_path_.Set(value, GetArenaForAllocation());
}
inline std::string* SensitiveExtractParam::_internal_mutable_file_path() {
  
  return _impl_.file_path_.Mutable(GetArenaForAllocation());
}
inline std::string* SensitiveExtractParam::release_file_path() {
  // @@protoc_insertion_point(field_release:SensitiveEngine.SensitiveExtractParam.file_path)
  return _impl_.file_path_.Release();
}
inline void SensitiveExtractParam::set_allocated_file_path(std::string* file_path) {
  if (file_path != nullptr) {
    
  } else {
    
  }
  _impl_.file_path_.SetAllocated(file_path, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.file_path_.IsDefault()) {
    _impl_.file_path_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:SensitiveEngine.SensitiveExtractParam.file_path)
}

// bytes file_stream = 3;
inline void SensitiveExtractParam::clear_file_stream() {
  _impl_.file_stream_.ClearToEmpty();
}
inline const std::string& SensitiveExtractParam::file_stream() const {
  // @@protoc_insertion_point(field_get:SensitiveEngine.SensitiveExtractParam.file_stream)
  return _internal_file_stream();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SensitiveExtractParam::set_file_stream(ArgT0&& arg0, ArgT... args) {
 
 _impl_.file_stream_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:SensitiveEngine.SensitiveExtractParam.file_stream)
}
inline std::string* SensitiveExtractParam::mutable_file_stream() {
  std::string* _s = _internal_mutable_file_stream();
  // @@protoc_insertion_point(field_mutable:SensitiveEngine.SensitiveExtractParam.file_stream)
  return _s;
}
inline const std::string& SensitiveExtractParam::_internal_file_stream() const {
  return _impl_.file_stream_.Get();
}
inline void SensitiveExtractParam::_internal_set_file_stream(const std::string& value) {
  
  _impl_.file_stream_.Set(value, GetArenaForAllocation());
}
inline std::string* SensitiveExtractParam::_internal_mutable_file_stream() {
  
  return _impl_.file_stream_.Mutable(GetArenaForAllocation());
}
inline std::string* SensitiveExtractParam::release_file_stream() {
  // @@protoc_insertion_point(field_release:SensitiveEngine.SensitiveExtractParam.file_stream)
  return _impl_.file_stream_.Release();
}
inline void SensitiveExtractParam::set_allocated_file_stream(std::string* file_stream) {
  if (file_stream != nullptr) {
    
  } else {
    
  }
  _impl_.file_stream_.SetAllocated(file_stream, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.file_stream_.IsDefault()) {
    _impl_.file_stream_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:SensitiveEngine.SensitiveExtractParam.file_stream)
}

// string src_proc = 4;
inline void SensitiveExtractParam::clear_src_proc() {
  _impl_.src_proc_.ClearToEmpty();
}
inline const std::string& SensitiveExtractParam::src_proc() const {
  // @@protoc_insertion_point(field_get:SensitiveEngine.SensitiveExtractParam.src_proc)
  return _internal_src_proc();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SensitiveExtractParam::set_src_proc(ArgT0&& arg0, ArgT... args) {
 
 _impl_.src_proc_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:SensitiveEngine.SensitiveExtractParam.src_proc)
}
inline std::string* SensitiveExtractParam::mutable_src_proc() {
  std::string* _s = _internal_mutable_src_proc();
  // @@protoc_insertion_point(field_mutable:SensitiveEngine.SensitiveExtractParam.src_proc)
  return _s;
}
inline const std::string& SensitiveExtractParam::_internal_src_proc() const {
  return _impl_.src_proc_.Get();
}
inline void SensitiveExtractParam::_internal_set_src_proc(const std::string& value) {
  
  _impl_.src_proc_.Set(value, GetArenaForAllocation());
}
inline std::string* SensitiveExtractParam::_internal_mutable_src_proc() {
  
  return _impl_.src_proc_.Mutable(GetArenaForAllocation());
}
inline std::string* SensitiveExtractParam::release_src_proc() {
  // @@protoc_insertion_point(field_release:SensitiveEngine.SensitiveExtractParam.src_proc)
  return _impl_.src_proc_.Release();
}
inline void SensitiveExtractParam::set_allocated_src_proc(std::string* src_proc) {
  if (src_proc != nullptr) {
    
  } else {
    
  }
  _impl_.src_proc_.SetAllocated(src_proc, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.src_proc_.IsDefault()) {
    _impl_.src_proc_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:SensitiveEngine.SensitiveExtractParam.src_proc)
}

// string src_url = 5;
inline void SensitiveExtractParam::clear_src_url() {
  _impl_.src_url_.ClearToEmpty();
}
inline const std::string& SensitiveExtractParam::src_url() const {
  // @@protoc_insertion_point(field_get:SensitiveEngine.SensitiveExtractParam.src_url)
  return _internal_src_url();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SensitiveExtractParam::set_src_url(ArgT0&& arg0, ArgT... args) {
 
 _impl_.src_url_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:SensitiveEngine.SensitiveExtractParam.src_url)
}
inline std::string* SensitiveExtractParam::mutable_src_url() {
  std::string* _s = _internal_mutable_src_url();
  // @@protoc_insertion_point(field_mutable:SensitiveEngine.SensitiveExtractParam.src_url)
  return _s;
}
inline const std::string& SensitiveExtractParam::_internal_src_url() const {
  return _impl_.src_url_.Get();
}
inline void SensitiveExtractParam::_internal_set_src_url(const std::string& value) {
  
  _impl_.src_url_.Set(value, GetArenaForAllocation());
}
inline std::string* SensitiveExtractParam::_internal_mutable_src_url() {
  
  return _impl_.src_url_.Mutable(GetArenaForAllocation());
}
inline std::string* SensitiveExtractParam::release_src_url() {
  // @@protoc_insertion_point(field_release:SensitiveEngine.SensitiveExtractParam.src_url)
  return _impl_.src_url_.Release();
}
inline void SensitiveExtractParam::set_allocated_src_url(std::string* src_url) {
  if (src_url != nullptr) {
    
  } else {
    
  }
  _impl_.src_url_.SetAllocated(src_url, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.src_url_.IsDefault()) {
    _impl_.src_url_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:SensitiveEngine.SensitiveExtractParam.src_url)
}

// .SensitiveEngine.SensitiveExtractParam.MatchType match_type = 6;
inline void SensitiveExtractParam::clear_match_type() {
  _impl_.match_type_ = 0;
}
inline ::SensitiveEngine::SensitiveExtractParam_MatchType SensitiveExtractParam::_internal_match_type() const {
  return static_cast< ::SensitiveEngine::SensitiveExtractParam_MatchType >(_impl_.match_type_);
}
inline ::SensitiveEngine::SensitiveExtractParam_MatchType SensitiveExtractParam::match_type() const {
  // @@protoc_insertion_point(field_get:SensitiveEngine.SensitiveExtractParam.match_type)
  return _internal_match_type();
}
inline void SensitiveExtractParam::_internal_set_match_type(::SensitiveEngine::SensitiveExtractParam_MatchType value) {
  
  _impl_.match_type_ = value;
}
inline void SensitiveExtractParam::set_match_type(::SensitiveEngine::SensitiveExtractParam_MatchType value) {
  _internal_set_match_type(value);
  // @@protoc_insertion_point(field_set:SensitiveEngine.SensitiveExtractParam.match_type)
}

// .SensitiveEngine.SensitiveExtractParam.MatchMode match_mode = 7;
inline void SensitiveExtractParam::clear_match_mode() {
  _impl_.match_mode_ = 0;
}
inline ::SensitiveEngine::SensitiveExtractParam_MatchMode SensitiveExtractParam::_internal_match_mode() const {
  return static_cast< ::SensitiveEngine::SensitiveExtractParam_MatchMode >(_impl_.match_mode_);
}
inline ::SensitiveEngine::SensitiveExtractParam_MatchMode SensitiveExtractParam::match_mode() const {
  // @@protoc_insertion_point(field_get:SensitiveEngine.SensitiveExtractParam.match_mode)
  return _internal_match_mode();
}
inline void SensitiveExtractParam::_internal_set_match_mode(::SensitiveEngine::SensitiveExtractParam_MatchMode value) {
  
  _impl_.match_mode_ = value;
}
inline void SensitiveExtractParam::set_match_mode(::SensitiveEngine::SensitiveExtractParam_MatchMode value) {
  _internal_set_match_mode(value);
  // @@protoc_insertion_point(field_set:SensitiveEngine.SensitiveExtractParam.match_mode)
}

// bool update_cache = 8;
inline void SensitiveExtractParam::clear_update_cache() {
  _impl_.update_cache_ = false;
}
inline bool SensitiveExtractParam::_internal_update_cache() const {
  return _impl_.update_cache_;
}
inline bool SensitiveExtractParam::update_cache() const {
  // @@protoc_insertion_point(field_get:SensitiveEngine.SensitiveExtractParam.update_cache)
  return _internal_update_cache();
}
inline void SensitiveExtractParam::_internal_set_update_cache(bool value) {
  
  _impl_.update_cache_ = value;
}
inline void SensitiveExtractParam::set_update_cache(bool value) {
  _internal_set_update_cache(value);
  // @@protoc_insertion_point(field_set:SensitiveEngine.SensitiveExtractParam.update_cache)
}

// -------------------------------------------------------------------

// SensitiveElementInfo

// int32 rule_id = 1;
inline void SensitiveElementInfo::clear_rule_id() {
  _impl_.rule_id_ = 0;
}
inline int32_t SensitiveElementInfo::_internal_rule_id() const {
  return _impl_.rule_id_;
}
inline int32_t SensitiveElementInfo::rule_id() const {
  // @@protoc_insertion_point(field_get:SensitiveEngine.SensitiveElementInfo.rule_id)
  return _internal_rule_id();
}
inline void SensitiveElementInfo::_internal_set_rule_id(int32_t value) {
  
  _impl_.rule_id_ = value;
}
inline void SensitiveElementInfo::set_rule_id(int32_t value) {
  _internal_set_rule_id(value);
  // @@protoc_insertion_point(field_set:SensitiveEngine.SensitiveElementInfo.rule_id)
}

// string desc = 2;
inline void SensitiveElementInfo::clear_desc() {
  _impl_.desc_.ClearToEmpty();
}
inline const std::string& SensitiveElementInfo::desc() const {
  // @@protoc_insertion_point(field_get:SensitiveEngine.SensitiveElementInfo.desc)
  return _internal_desc();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SensitiveElementInfo::set_desc(ArgT0&& arg0, ArgT... args) {
 
 _impl_.desc_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:SensitiveEngine.SensitiveElementInfo.desc)
}
inline std::string* SensitiveElementInfo::mutable_desc() {
  std::string* _s = _internal_mutable_desc();
  // @@protoc_insertion_point(field_mutable:SensitiveEngine.SensitiveElementInfo.desc)
  return _s;
}
inline const std::string& SensitiveElementInfo::_internal_desc() const {
  return _impl_.desc_.Get();
}
inline void SensitiveElementInfo::_internal_set_desc(const std::string& value) {
  
  _impl_.desc_.Set(value, GetArenaForAllocation());
}
inline std::string* SensitiveElementInfo::_internal_mutable_desc() {
  
  return _impl_.desc_.Mutable(GetArenaForAllocation());
}
inline std::string* SensitiveElementInfo::release_desc() {
  // @@protoc_insertion_point(field_release:SensitiveEngine.SensitiveElementInfo.desc)
  return _impl_.desc_.Release();
}
inline void SensitiveElementInfo::set_allocated_desc(std::string* desc) {
  if (desc != nullptr) {
    
  } else {
    
  }
  _impl_.desc_.SetAllocated(desc, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.desc_.IsDefault()) {
    _impl_.desc_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:SensitiveEngine.SensitiveElementInfo.desc)
}

// repeated string sensitive_element_data = 3;
inline int SensitiveElementInfo::_internal_sensitive_element_data_size() const {
  return _impl_.sensitive_element_data_.size();
}
inline int SensitiveElementInfo::sensitive_element_data_size() const {
  return _internal_sensitive_element_data_size();
}
inline void SensitiveElementInfo::clear_sensitive_element_data() {
  _impl_.sensitive_element_data_.Clear();
}
inline std::string* SensitiveElementInfo::add_sensitive_element_data() {
  std::string* _s = _internal_add_sensitive_element_data();
  // @@protoc_insertion_point(field_add_mutable:SensitiveEngine.SensitiveElementInfo.sensitive_element_data)
  return _s;
}
inline const std::string& SensitiveElementInfo::_internal_sensitive_element_data(int index) const {
  return _impl_.sensitive_element_data_.Get(index);
}
inline const std::string& SensitiveElementInfo::sensitive_element_data(int index) const {
  // @@protoc_insertion_point(field_get:SensitiveEngine.SensitiveElementInfo.sensitive_element_data)
  return _internal_sensitive_element_data(index);
}
inline std::string* SensitiveElementInfo::mutable_sensitive_element_data(int index) {
  // @@protoc_insertion_point(field_mutable:SensitiveEngine.SensitiveElementInfo.sensitive_element_data)
  return _impl_.sensitive_element_data_.Mutable(index);
}
inline void SensitiveElementInfo::set_sensitive_element_data(int index, const std::string& value) {
  _impl_.sensitive_element_data_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:SensitiveEngine.SensitiveElementInfo.sensitive_element_data)
}
inline void SensitiveElementInfo::set_sensitive_element_data(int index, std::string&& value) {
  _impl_.sensitive_element_data_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:SensitiveEngine.SensitiveElementInfo.sensitive_element_data)
}
inline void SensitiveElementInfo::set_sensitive_element_data(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.sensitive_element_data_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:SensitiveEngine.SensitiveElementInfo.sensitive_element_data)
}
inline void SensitiveElementInfo::set_sensitive_element_data(int index, const char* value, size_t size) {
  _impl_.sensitive_element_data_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:SensitiveEngine.SensitiveElementInfo.sensitive_element_data)
}
inline std::string* SensitiveElementInfo::_internal_add_sensitive_element_data() {
  return _impl_.sensitive_element_data_.Add();
}
inline void SensitiveElementInfo::add_sensitive_element_data(const std::string& value) {
  _impl_.sensitive_element_data_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:SensitiveEngine.SensitiveElementInfo.sensitive_element_data)
}
inline void SensitiveElementInfo::add_sensitive_element_data(std::string&& value) {
  _impl_.sensitive_element_data_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:SensitiveEngine.SensitiveElementInfo.sensitive_element_data)
}
inline void SensitiveElementInfo::add_sensitive_element_data(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.sensitive_element_data_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:SensitiveEngine.SensitiveElementInfo.sensitive_element_data)
}
inline void SensitiveElementInfo::add_sensitive_element_data(const char* value, size_t size) {
  _impl_.sensitive_element_data_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:SensitiveEngine.SensitiveElementInfo.sensitive_element_data)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
SensitiveElementInfo::sensitive_element_data() const {
  // @@protoc_insertion_point(field_list:SensitiveEngine.SensitiveElementInfo.sensitive_element_data)
  return _impl_.sensitive_element_data_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
SensitiveElementInfo::mutable_sensitive_element_data() {
  // @@protoc_insertion_point(field_mutable_list:SensitiveEngine.SensitiveElementInfo.sensitive_element_data)
  return &_impl_.sensitive_element_data_;
}

// -------------------------------------------------------------------

// SensitiveDataInfo

// bool hit = 1;
inline void SensitiveDataInfo::clear_hit() {
  _impl_.hit_ = false;
}
inline bool SensitiveDataInfo::_internal_hit() const {
  return _impl_.hit_;
}
inline bool SensitiveDataInfo::hit() const {
  // @@protoc_insertion_point(field_get:SensitiveEngine.SensitiveDataInfo.hit)
  return _internal_hit();
}
inline void SensitiveDataInfo::_internal_set_hit(bool value) {
  
  _impl_.hit_ = value;
}
inline void SensitiveDataInfo::set_hit(bool value) {
  _internal_set_hit(value);
  // @@protoc_insertion_point(field_set:SensitiveEngine.SensitiveDataInfo.hit)
}

// string sensitive_data_id = 2;
inline void SensitiveDataInfo::clear_sensitive_data_id() {
  _impl_.sensitive_data_id_.ClearToEmpty();
}
inline const std::string& SensitiveDataInfo::sensitive_data_id() const {
  // @@protoc_insertion_point(field_get:SensitiveEngine.SensitiveDataInfo.sensitive_data_id)
  return _internal_sensitive_data_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SensitiveDataInfo::set_sensitive_data_id(ArgT0&& arg0, ArgT... args) {
 
 _impl_.sensitive_data_id_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:SensitiveEngine.SensitiveDataInfo.sensitive_data_id)
}
inline std::string* SensitiveDataInfo::mutable_sensitive_data_id() {
  std::string* _s = _internal_mutable_sensitive_data_id();
  // @@protoc_insertion_point(field_mutable:SensitiveEngine.SensitiveDataInfo.sensitive_data_id)
  return _s;
}
inline const std::string& SensitiveDataInfo::_internal_sensitive_data_id() const {
  return _impl_.sensitive_data_id_.Get();
}
inline void SensitiveDataInfo::_internal_set_sensitive_data_id(const std::string& value) {
  
  _impl_.sensitive_data_id_.Set(value, GetArenaForAllocation());
}
inline std::string* SensitiveDataInfo::_internal_mutable_sensitive_data_id() {
  
  return _impl_.sensitive_data_id_.Mutable(GetArenaForAllocation());
}
inline std::string* SensitiveDataInfo::release_sensitive_data_id() {
  // @@protoc_insertion_point(field_release:SensitiveEngine.SensitiveDataInfo.sensitive_data_id)
  return _impl_.sensitive_data_id_.Release();
}
inline void SensitiveDataInfo::set_allocated_sensitive_data_id(std::string* sensitive_data_id) {
  if (sensitive_data_id != nullptr) {
    
  } else {
    
  }
  _impl_.sensitive_data_id_.SetAllocated(sensitive_data_id, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.sensitive_data_id_.IsDefault()) {
    _impl_.sensitive_data_id_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:SensitiveEngine.SensitiveDataInfo.sensitive_data_id)
}

// string sensitive_data_name = 3;
inline void SensitiveDataInfo::clear_sensitive_data_name() {
  _impl_.sensitive_data_name_.ClearToEmpty();
}
inline const std::string& SensitiveDataInfo::sensitive_data_name() const {
  // @@protoc_insertion_point(field_get:SensitiveEngine.SensitiveDataInfo.sensitive_data_name)
  return _internal_sensitive_data_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SensitiveDataInfo::set_sensitive_data_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.sensitive_data_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:SensitiveEngine.SensitiveDataInfo.sensitive_data_name)
}
inline std::string* SensitiveDataInfo::mutable_sensitive_data_name() {
  std::string* _s = _internal_mutable_sensitive_data_name();
  // @@protoc_insertion_point(field_mutable:SensitiveEngine.SensitiveDataInfo.sensitive_data_name)
  return _s;
}
inline const std::string& SensitiveDataInfo::_internal_sensitive_data_name() const {
  return _impl_.sensitive_data_name_.Get();
}
inline void SensitiveDataInfo::_internal_set_sensitive_data_name(const std::string& value) {
  
  _impl_.sensitive_data_name_.Set(value, GetArenaForAllocation());
}
inline std::string* SensitiveDataInfo::_internal_mutable_sensitive_data_name() {
  
  return _impl_.sensitive_data_name_.Mutable(GetArenaForAllocation());
}
inline std::string* SensitiveDataInfo::release_sensitive_data_name() {
  // @@protoc_insertion_point(field_release:SensitiveEngine.SensitiveDataInfo.sensitive_data_name)
  return _impl_.sensitive_data_name_.Release();
}
inline void SensitiveDataInfo::set_allocated_sensitive_data_name(std::string* sensitive_data_name) {
  if (sensitive_data_name != nullptr) {
    
  } else {
    
  }
  _impl_.sensitive_data_name_.SetAllocated(sensitive_data_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.sensitive_data_name_.IsDefault()) {
    _impl_.sensitive_data_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:SensitiveEngine.SensitiveDataInfo.sensitive_data_name)
}

// int32 sensitive_data_level = 4;
inline void SensitiveDataInfo::clear_sensitive_data_level() {
  _impl_.sensitive_data_level_ = 0;
}
inline int32_t SensitiveDataInfo::_internal_sensitive_data_level() const {
  return _impl_.sensitive_data_level_;
}
inline int32_t SensitiveDataInfo::sensitive_data_level() const {
  // @@protoc_insertion_point(field_get:SensitiveEngine.SensitiveDataInfo.sensitive_data_level)
  return _internal_sensitive_data_level();
}
inline void SensitiveDataInfo::_internal_set_sensitive_data_level(int32_t value) {
  
  _impl_.sensitive_data_level_ = value;
}
inline void SensitiveDataInfo::set_sensitive_data_level(int32_t value) {
  _internal_set_sensitive_data_level(value);
  // @@protoc_insertion_point(field_set:SensitiveEngine.SensitiveDataInfo.sensitive_data_level)
}

// string sensitive_data_category_id = 5;
inline void SensitiveDataInfo::clear_sensitive_data_category_id() {
  _impl_.sensitive_data_category_id_.ClearToEmpty();
}
inline const std::string& SensitiveDataInfo::sensitive_data_category_id() const {
  // @@protoc_insertion_point(field_get:SensitiveEngine.SensitiveDataInfo.sensitive_data_category_id)
  return _internal_sensitive_data_category_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SensitiveDataInfo::set_sensitive_data_category_id(ArgT0&& arg0, ArgT... args) {
 
 _impl_.sensitive_data_category_id_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:SensitiveEngine.SensitiveDataInfo.sensitive_data_category_id)
}
inline std::string* SensitiveDataInfo::mutable_sensitive_data_category_id() {
  std::string* _s = _internal_mutable_sensitive_data_category_id();
  // @@protoc_insertion_point(field_mutable:SensitiveEngine.SensitiveDataInfo.sensitive_data_category_id)
  return _s;
}
inline const std::string& SensitiveDataInfo::_internal_sensitive_data_category_id() const {
  return _impl_.sensitive_data_category_id_.Get();
}
inline void SensitiveDataInfo::_internal_set_sensitive_data_category_id(const std::string& value) {
  
  _impl_.sensitive_data_category_id_.Set(value, GetArenaForAllocation());
}
inline std::string* SensitiveDataInfo::_internal_mutable_sensitive_data_category_id() {
  
  return _impl_.sensitive_data_category_id_.Mutable(GetArenaForAllocation());
}
inline std::string* SensitiveDataInfo::release_sensitive_data_category_id() {
  // @@protoc_insertion_point(field_release:SensitiveEngine.SensitiveDataInfo.sensitive_data_category_id)
  return _impl_.sensitive_data_category_id_.Release();
}
inline void SensitiveDataInfo::set_allocated_sensitive_data_category_id(std::string* sensitive_data_category_id) {
  if (sensitive_data_category_id != nullptr) {
    
  } else {
    
  }
  _impl_.sensitive_data_category_id_.SetAllocated(sensitive_data_category_id, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.sensitive_data_category_id_.IsDefault()) {
    _impl_.sensitive_data_category_id_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:SensitiveEngine.SensitiveDataInfo.sensitive_data_category_id)
}

// repeated string sensitive_source_id = 6;
inline int SensitiveDataInfo::_internal_sensitive_source_id_size() const {
  return _impl_.sensitive_source_id_.size();
}
inline int SensitiveDataInfo::sensitive_source_id_size() const {
  return _internal_sensitive_source_id_size();
}
inline void SensitiveDataInfo::clear_sensitive_source_id() {
  _impl_.sensitive_source_id_.Clear();
}
inline std::string* SensitiveDataInfo::add_sensitive_source_id() {
  std::string* _s = _internal_add_sensitive_source_id();
  // @@protoc_insertion_point(field_add_mutable:SensitiveEngine.SensitiveDataInfo.sensitive_source_id)
  return _s;
}
inline const std::string& SensitiveDataInfo::_internal_sensitive_source_id(int index) const {
  return _impl_.sensitive_source_id_.Get(index);
}
inline const std::string& SensitiveDataInfo::sensitive_source_id(int index) const {
  // @@protoc_insertion_point(field_get:SensitiveEngine.SensitiveDataInfo.sensitive_source_id)
  return _internal_sensitive_source_id(index);
}
inline std::string* SensitiveDataInfo::mutable_sensitive_source_id(int index) {
  // @@protoc_insertion_point(field_mutable:SensitiveEngine.SensitiveDataInfo.sensitive_source_id)
  return _impl_.sensitive_source_id_.Mutable(index);
}
inline void SensitiveDataInfo::set_sensitive_source_id(int index, const std::string& value) {
  _impl_.sensitive_source_id_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:SensitiveEngine.SensitiveDataInfo.sensitive_source_id)
}
inline void SensitiveDataInfo::set_sensitive_source_id(int index, std::string&& value) {
  _impl_.sensitive_source_id_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:SensitiveEngine.SensitiveDataInfo.sensitive_source_id)
}
inline void SensitiveDataInfo::set_sensitive_source_id(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.sensitive_source_id_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:SensitiveEngine.SensitiveDataInfo.sensitive_source_id)
}
inline void SensitiveDataInfo::set_sensitive_source_id(int index, const char* value, size_t size) {
  _impl_.sensitive_source_id_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:SensitiveEngine.SensitiveDataInfo.sensitive_source_id)
}
inline std::string* SensitiveDataInfo::_internal_add_sensitive_source_id() {
  return _impl_.sensitive_source_id_.Add();
}
inline void SensitiveDataInfo::add_sensitive_source_id(const std::string& value) {
  _impl_.sensitive_source_id_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:SensitiveEngine.SensitiveDataInfo.sensitive_source_id)
}
inline void SensitiveDataInfo::add_sensitive_source_id(std::string&& value) {
  _impl_.sensitive_source_id_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:SensitiveEngine.SensitiveDataInfo.sensitive_source_id)
}
inline void SensitiveDataInfo::add_sensitive_source_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.sensitive_source_id_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:SensitiveEngine.SensitiveDataInfo.sensitive_source_id)
}
inline void SensitiveDataInfo::add_sensitive_source_id(const char* value, size_t size) {
  _impl_.sensitive_source_id_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:SensitiveEngine.SensitiveDataInfo.sensitive_source_id)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
SensitiveDataInfo::sensitive_source_id() const {
  // @@protoc_insertion_point(field_list:SensitiveEngine.SensitiveDataInfo.sensitive_source_id)
  return _impl_.sensitive_source_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
SensitiveDataInfo::mutable_sensitive_source_id() {
  // @@protoc_insertion_point(field_mutable_list:SensitiveEngine.SensitiveDataInfo.sensitive_source_id)
  return &_impl_.sensitive_source_id_;
}

// repeated string src_paths = 7;
inline int SensitiveDataInfo::_internal_src_paths_size() const {
  return _impl_.src_paths_.size();
}
inline int SensitiveDataInfo::src_paths_size() const {
  return _internal_src_paths_size();
}
inline void SensitiveDataInfo::clear_src_paths() {
  _impl_.src_paths_.Clear();
}
inline std::string* SensitiveDataInfo::add_src_paths() {
  std::string* _s = _internal_add_src_paths();
  // @@protoc_insertion_point(field_add_mutable:SensitiveEngine.SensitiveDataInfo.src_paths)
  return _s;
}
inline const std::string& SensitiveDataInfo::_internal_src_paths(int index) const {
  return _impl_.src_paths_.Get(index);
}
inline const std::string& SensitiveDataInfo::src_paths(int index) const {
  // @@protoc_insertion_point(field_get:SensitiveEngine.SensitiveDataInfo.src_paths)
  return _internal_src_paths(index);
}
inline std::string* SensitiveDataInfo::mutable_src_paths(int index) {
  // @@protoc_insertion_point(field_mutable:SensitiveEngine.SensitiveDataInfo.src_paths)
  return _impl_.src_paths_.Mutable(index);
}
inline void SensitiveDataInfo::set_src_paths(int index, const std::string& value) {
  _impl_.src_paths_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:SensitiveEngine.SensitiveDataInfo.src_paths)
}
inline void SensitiveDataInfo::set_src_paths(int index, std::string&& value) {
  _impl_.src_paths_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:SensitiveEngine.SensitiveDataInfo.src_paths)
}
inline void SensitiveDataInfo::set_src_paths(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.src_paths_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:SensitiveEngine.SensitiveDataInfo.src_paths)
}
inline void SensitiveDataInfo::set_src_paths(int index, const char* value, size_t size) {
  _impl_.src_paths_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:SensitiveEngine.SensitiveDataInfo.src_paths)
}
inline std::string* SensitiveDataInfo::_internal_add_src_paths() {
  return _impl_.src_paths_.Add();
}
inline void SensitiveDataInfo::add_src_paths(const std::string& value) {
  _impl_.src_paths_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:SensitiveEngine.SensitiveDataInfo.src_paths)
}
inline void SensitiveDataInfo::add_src_paths(std::string&& value) {
  _impl_.src_paths_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:SensitiveEngine.SensitiveDataInfo.src_paths)
}
inline void SensitiveDataInfo::add_src_paths(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.src_paths_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:SensitiveEngine.SensitiveDataInfo.src_paths)
}
inline void SensitiveDataInfo::add_src_paths(const char* value, size_t size) {
  _impl_.src_paths_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:SensitiveEngine.SensitiveDataInfo.src_paths)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
SensitiveDataInfo::src_paths() const {
  // @@protoc_insertion_point(field_list:SensitiveEngine.SensitiveDataInfo.src_paths)
  return _impl_.src_paths_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
SensitiveDataInfo::mutable_src_paths() {
  // @@protoc_insertion_point(field_mutable_list:SensitiveEngine.SensitiveDataInfo.src_paths)
  return &_impl_.src_paths_;
}

// repeated .SensitiveEngine.SensitiveElementInfo file_name_result = 8;
inline int SensitiveDataInfo::_internal_file_name_result_size() const {
  return _impl_.file_name_result_.size();
}
inline int SensitiveDataInfo::file_name_result_size() const {
  return _internal_file_name_result_size();
}
inline void SensitiveDataInfo::clear_file_name_result() {
  _impl_.file_name_result_.Clear();
}
inline ::SensitiveEngine::SensitiveElementInfo* SensitiveDataInfo::mutable_file_name_result(int index) {
  // @@protoc_insertion_point(field_mutable:SensitiveEngine.SensitiveDataInfo.file_name_result)
  return _impl_.file_name_result_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::SensitiveEngine::SensitiveElementInfo >*
SensitiveDataInfo::mutable_file_name_result() {
  // @@protoc_insertion_point(field_mutable_list:SensitiveEngine.SensitiveDataInfo.file_name_result)
  return &_impl_.file_name_result_;
}
inline const ::SensitiveEngine::SensitiveElementInfo& SensitiveDataInfo::_internal_file_name_result(int index) const {
  return _impl_.file_name_result_.Get(index);
}
inline const ::SensitiveEngine::SensitiveElementInfo& SensitiveDataInfo::file_name_result(int index) const {
  // @@protoc_insertion_point(field_get:SensitiveEngine.SensitiveDataInfo.file_name_result)
  return _internal_file_name_result(index);
}
inline ::SensitiveEngine::SensitiveElementInfo* SensitiveDataInfo::_internal_add_file_name_result() {
  return _impl_.file_name_result_.Add();
}
inline ::SensitiveEngine::SensitiveElementInfo* SensitiveDataInfo::add_file_name_result() {
  ::SensitiveEngine::SensitiveElementInfo* _add = _internal_add_file_name_result();
  // @@protoc_insertion_point(field_add:SensitiveEngine.SensitiveDataInfo.file_name_result)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::SensitiveEngine::SensitiveElementInfo >&
SensitiveDataInfo::file_name_result() const {
  // @@protoc_insertion_point(field_list:SensitiveEngine.SensitiveDataInfo.file_name_result)
  return _impl_.file_name_result_;
}

// repeated .SensitiveEngine.SensitiveElementInfo file_content_result = 9;
inline int SensitiveDataInfo::_internal_file_content_result_size() const {
  return _impl_.file_content_result_.size();
}
inline int SensitiveDataInfo::file_content_result_size() const {
  return _internal_file_content_result_size();
}
inline void SensitiveDataInfo::clear_file_content_result() {
  _impl_.file_content_result_.Clear();
}
inline ::SensitiveEngine::SensitiveElementInfo* SensitiveDataInfo::mutable_file_content_result(int index) {
  // @@protoc_insertion_point(field_mutable:SensitiveEngine.SensitiveDataInfo.file_content_result)
  return _impl_.file_content_result_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::SensitiveEngine::SensitiveElementInfo >*
SensitiveDataInfo::mutable_file_content_result() {
  // @@protoc_insertion_point(field_mutable_list:SensitiveEngine.SensitiveDataInfo.file_content_result)
  return &_impl_.file_content_result_;
}
inline const ::SensitiveEngine::SensitiveElementInfo& SensitiveDataInfo::_internal_file_content_result(int index) const {
  return _impl_.file_content_result_.Get(index);
}
inline const ::SensitiveEngine::SensitiveElementInfo& SensitiveDataInfo::file_content_result(int index) const {
  // @@protoc_insertion_point(field_get:SensitiveEngine.SensitiveDataInfo.file_content_result)
  return _internal_file_content_result(index);
}
inline ::SensitiveEngine::SensitiveElementInfo* SensitiveDataInfo::_internal_add_file_content_result() {
  return _impl_.file_content_result_.Add();
}
inline ::SensitiveEngine::SensitiveElementInfo* SensitiveDataInfo::add_file_content_result() {
  ::SensitiveEngine::SensitiveElementInfo* _add = _internal_add_file_content_result();
  // @@protoc_insertion_point(field_add:SensitiveEngine.SensitiveDataInfo.file_content_result)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::SensitiveEngine::SensitiveElementInfo >&
SensitiveDataInfo::file_content_result() const {
  // @@protoc_insertion_point(field_list:SensitiveEngine.SensitiveDataInfo.file_content_result)
  return _impl_.file_content_result_;
}

// repeated .SensitiveEngine.SensitiveElementInfo file_text_result = 10;
inline int SensitiveDataInfo::_internal_file_text_result_size() const {
  return _impl_.file_text_result_.size();
}
inline int SensitiveDataInfo::file_text_result_size() const {
  return _internal_file_text_result_size();
}
inline void SensitiveDataInfo::clear_file_text_result() {
  _impl_.file_text_result_.Clear();
}
inline ::SensitiveEngine::SensitiveElementInfo* SensitiveDataInfo::mutable_file_text_result(int index) {
  // @@protoc_insertion_point(field_mutable:SensitiveEngine.SensitiveDataInfo.file_text_result)
  return _impl_.file_text_result_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::SensitiveEngine::SensitiveElementInfo >*
SensitiveDataInfo::mutable_file_text_result() {
  // @@protoc_insertion_point(field_mutable_list:SensitiveEngine.SensitiveDataInfo.file_text_result)
  return &_impl_.file_text_result_;
}
inline const ::SensitiveEngine::SensitiveElementInfo& SensitiveDataInfo::_internal_file_text_result(int index) const {
  return _impl_.file_text_result_.Get(index);
}
inline const ::SensitiveEngine::SensitiveElementInfo& SensitiveDataInfo::file_text_result(int index) const {
  // @@protoc_insertion_point(field_get:SensitiveEngine.SensitiveDataInfo.file_text_result)
  return _internal_file_text_result(index);
}
inline ::SensitiveEngine::SensitiveElementInfo* SensitiveDataInfo::_internal_add_file_text_result() {
  return _impl_.file_text_result_.Add();
}
inline ::SensitiveEngine::SensitiveElementInfo* SensitiveDataInfo::add_file_text_result() {
  ::SensitiveEngine::SensitiveElementInfo* _add = _internal_add_file_text_result();
  // @@protoc_insertion_point(field_add:SensitiveEngine.SensitiveDataInfo.file_text_result)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::SensitiveEngine::SensitiveElementInfo >&
SensitiveDataInfo::file_text_result() const {
  // @@protoc_insertion_point(field_list:SensitiveEngine.SensitiveDataInfo.file_text_result)
  return _impl_.file_text_result_;
}

// bool file_type_suffix = 11;
inline void SensitiveDataInfo::clear_file_type_suffix() {
  _impl_.file_type_suffix_ = false;
}
inline bool SensitiveDataInfo::_internal_file_type_suffix() const {
  return _impl_.file_type_suffix_;
}
inline bool SensitiveDataInfo::file_type_suffix() const {
  // @@protoc_insertion_point(field_get:SensitiveEngine.SensitiveDataInfo.file_type_suffix)
  return _internal_file_type_suffix();
}
inline void SensitiveDataInfo::_internal_set_file_type_suffix(bool value) {
  
  _impl_.file_type_suffix_ = value;
}
inline void SensitiveDataInfo::set_file_type_suffix(bool value) {
  _internal_set_file_type_suffix(value);
  // @@protoc_insertion_point(field_set:SensitiveEngine.SensitiveDataInfo.file_type_suffix)
}

// -------------------------------------------------------------------

// SensitiveExtractPackage

// repeated string trace_id = 1;
inline int SensitiveExtractPackage::_internal_trace_id_size() const {
  return _impl_.trace_id_.size();
}
inline int SensitiveExtractPackage::trace_id_size() const {
  return _internal_trace_id_size();
}
inline void SensitiveExtractPackage::clear_trace_id() {
  _impl_.trace_id_.Clear();
}
inline std::string* SensitiveExtractPackage::add_trace_id() {
  std::string* _s = _internal_add_trace_id();
  // @@protoc_insertion_point(field_add_mutable:SensitiveEngine.SensitiveExtractPackage.trace_id)
  return _s;
}
inline const std::string& SensitiveExtractPackage::_internal_trace_id(int index) const {
  return _impl_.trace_id_.Get(index);
}
inline const std::string& SensitiveExtractPackage::trace_id(int index) const {
  // @@protoc_insertion_point(field_get:SensitiveEngine.SensitiveExtractPackage.trace_id)
  return _internal_trace_id(index);
}
inline std::string* SensitiveExtractPackage::mutable_trace_id(int index) {
  // @@protoc_insertion_point(field_mutable:SensitiveEngine.SensitiveExtractPackage.trace_id)
  return _impl_.trace_id_.Mutable(index);
}
inline void SensitiveExtractPackage::set_trace_id(int index, const std::string& value) {
  _impl_.trace_id_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:SensitiveEngine.SensitiveExtractPackage.trace_id)
}
inline void SensitiveExtractPackage::set_trace_id(int index, std::string&& value) {
  _impl_.trace_id_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:SensitiveEngine.SensitiveExtractPackage.trace_id)
}
inline void SensitiveExtractPackage::set_trace_id(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.trace_id_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:SensitiveEngine.SensitiveExtractPackage.trace_id)
}
inline void SensitiveExtractPackage::set_trace_id(int index, const char* value, size_t size) {
  _impl_.trace_id_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:SensitiveEngine.SensitiveExtractPackage.trace_id)
}
inline std::string* SensitiveExtractPackage::_internal_add_trace_id() {
  return _impl_.trace_id_.Add();
}
inline void SensitiveExtractPackage::add_trace_id(const std::string& value) {
  _impl_.trace_id_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:SensitiveEngine.SensitiveExtractPackage.trace_id)
}
inline void SensitiveExtractPackage::add_trace_id(std::string&& value) {
  _impl_.trace_id_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:SensitiveEngine.SensitiveExtractPackage.trace_id)
}
inline void SensitiveExtractPackage::add_trace_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.trace_id_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:SensitiveEngine.SensitiveExtractPackage.trace_id)
}
inline void SensitiveExtractPackage::add_trace_id(const char* value, size_t size) {
  _impl_.trace_id_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:SensitiveEngine.SensitiveExtractPackage.trace_id)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
SensitiveExtractPackage::trace_id() const {
  // @@protoc_insertion_point(field_list:SensitiveEngine.SensitiveExtractPackage.trace_id)
  return _impl_.trace_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
SensitiveExtractPackage::mutable_trace_id() {
  // @@protoc_insertion_point(field_mutable_list:SensitiveEngine.SensitiveExtractPackage.trace_id)
  return &_impl_.trace_id_;
}

// repeated string sub_trace_id = 2;
inline int SensitiveExtractPackage::_internal_sub_trace_id_size() const {
  return _impl_.sub_trace_id_.size();
}
inline int SensitiveExtractPackage::sub_trace_id_size() const {
  return _internal_sub_trace_id_size();
}
inline void SensitiveExtractPackage::clear_sub_trace_id() {
  _impl_.sub_trace_id_.Clear();
}
inline std::string* SensitiveExtractPackage::add_sub_trace_id() {
  std::string* _s = _internal_add_sub_trace_id();
  // @@protoc_insertion_point(field_add_mutable:SensitiveEngine.SensitiveExtractPackage.sub_trace_id)
  return _s;
}
inline const std::string& SensitiveExtractPackage::_internal_sub_trace_id(int index) const {
  return _impl_.sub_trace_id_.Get(index);
}
inline const std::string& SensitiveExtractPackage::sub_trace_id(int index) const {
  // @@protoc_insertion_point(field_get:SensitiveEngine.SensitiveExtractPackage.sub_trace_id)
  return _internal_sub_trace_id(index);
}
inline std::string* SensitiveExtractPackage::mutable_sub_trace_id(int index) {
  // @@protoc_insertion_point(field_mutable:SensitiveEngine.SensitiveExtractPackage.sub_trace_id)
  return _impl_.sub_trace_id_.Mutable(index);
}
inline void SensitiveExtractPackage::set_sub_trace_id(int index, const std::string& value) {
  _impl_.sub_trace_id_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:SensitiveEngine.SensitiveExtractPackage.sub_trace_id)
}
inline void SensitiveExtractPackage::set_sub_trace_id(int index, std::string&& value) {
  _impl_.sub_trace_id_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:SensitiveEngine.SensitiveExtractPackage.sub_trace_id)
}
inline void SensitiveExtractPackage::set_sub_trace_id(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.sub_trace_id_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:SensitiveEngine.SensitiveExtractPackage.sub_trace_id)
}
inline void SensitiveExtractPackage::set_sub_trace_id(int index, const char* value, size_t size) {
  _impl_.sub_trace_id_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:SensitiveEngine.SensitiveExtractPackage.sub_trace_id)
}
inline std::string* SensitiveExtractPackage::_internal_add_sub_trace_id() {
  return _impl_.sub_trace_id_.Add();
}
inline void SensitiveExtractPackage::add_sub_trace_id(const std::string& value) {
  _impl_.sub_trace_id_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:SensitiveEngine.SensitiveExtractPackage.sub_trace_id)
}
inline void SensitiveExtractPackage::add_sub_trace_id(std::string&& value) {
  _impl_.sub_trace_id_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:SensitiveEngine.SensitiveExtractPackage.sub_trace_id)
}
inline void SensitiveExtractPackage::add_sub_trace_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.sub_trace_id_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:SensitiveEngine.SensitiveExtractPackage.sub_trace_id)
}
inline void SensitiveExtractPackage::add_sub_trace_id(const char* value, size_t size) {
  _impl_.sub_trace_id_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:SensitiveEngine.SensitiveExtractPackage.sub_trace_id)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
SensitiveExtractPackage::sub_trace_id() const {
  // @@protoc_insertion_point(field_list:SensitiveEngine.SensitiveExtractPackage.sub_trace_id)
  return _impl_.sub_trace_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
SensitiveExtractPackage::mutable_sub_trace_id() {
  // @@protoc_insertion_point(field_mutable_list:SensitiveEngine.SensitiveExtractPackage.sub_trace_id)
  return &_impl_.sub_trace_id_;
}

// int32 category_id = 3;
inline void SensitiveExtractPackage::clear_category_id() {
  _impl_.category_id_ = 0;
}
inline int32_t SensitiveExtractPackage::_internal_category_id() const {
  return _impl_.category_id_;
}
inline int32_t SensitiveExtractPackage::category_id() const {
  // @@protoc_insertion_point(field_get:SensitiveEngine.SensitiveExtractPackage.category_id)
  return _internal_category_id();
}
inline void SensitiveExtractPackage::_internal_set_category_id(int32_t value) {
  
  _impl_.category_id_ = value;
}
inline void SensitiveExtractPackage::set_category_id(int32_t value) {
  _internal_set_category_id(value);
  // @@protoc_insertion_point(field_set:SensitiveEngine.SensitiveExtractPackage.category_id)
}

// string extension_name = 4;
inline void SensitiveExtractPackage::clear_extension_name() {
  _impl_.extension_name_.ClearToEmpty();
}
inline const std::string& SensitiveExtractPackage::extension_name() const {
  // @@protoc_insertion_point(field_get:SensitiveEngine.SensitiveExtractPackage.extension_name)
  return _internal_extension_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SensitiveExtractPackage::set_extension_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.extension_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:SensitiveEngine.SensitiveExtractPackage.extension_name)
}
inline std::string* SensitiveExtractPackage::mutable_extension_name() {
  std::string* _s = _internal_mutable_extension_name();
  // @@protoc_insertion_point(field_mutable:SensitiveEngine.SensitiveExtractPackage.extension_name)
  return _s;
}
inline const std::string& SensitiveExtractPackage::_internal_extension_name() const {
  return _impl_.extension_name_.Get();
}
inline void SensitiveExtractPackage::_internal_set_extension_name(const std::string& value) {
  
  _impl_.extension_name_.Set(value, GetArenaForAllocation());
}
inline std::string* SensitiveExtractPackage::_internal_mutable_extension_name() {
  
  return _impl_.extension_name_.Mutable(GetArenaForAllocation());
}
inline std::string* SensitiveExtractPackage::release_extension_name() {
  // @@protoc_insertion_point(field_release:SensitiveEngine.SensitiveExtractPackage.extension_name)
  return _impl_.extension_name_.Release();
}
inline void SensitiveExtractPackage::set_allocated_extension_name(std::string* extension_name) {
  if (extension_name != nullptr) {
    
  } else {
    
  }
  _impl_.extension_name_.SetAllocated(extension_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.extension_name_.IsDefault()) {
    _impl_.extension_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:SensitiveEngine.SensitiveExtractPackage.extension_name)
}

// repeated .SensitiveEngine.SensitiveDataInfo sensitive_data_infos = 5;
inline int SensitiveExtractPackage::_internal_sensitive_data_infos_size() const {
  return _impl_.sensitive_data_infos_.size();
}
inline int SensitiveExtractPackage::sensitive_data_infos_size() const {
  return _internal_sensitive_data_infos_size();
}
inline void SensitiveExtractPackage::clear_sensitive_data_infos() {
  _impl_.sensitive_data_infos_.Clear();
}
inline ::SensitiveEngine::SensitiveDataInfo* SensitiveExtractPackage::mutable_sensitive_data_infos(int index) {
  // @@protoc_insertion_point(field_mutable:SensitiveEngine.SensitiveExtractPackage.sensitive_data_infos)
  return _impl_.sensitive_data_infos_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::SensitiveEngine::SensitiveDataInfo >*
SensitiveExtractPackage::mutable_sensitive_data_infos() {
  // @@protoc_insertion_point(field_mutable_list:SensitiveEngine.SensitiveExtractPackage.sensitive_data_infos)
  return &_impl_.sensitive_data_infos_;
}
inline const ::SensitiveEngine::SensitiveDataInfo& SensitiveExtractPackage::_internal_sensitive_data_infos(int index) const {
  return _impl_.sensitive_data_infos_.Get(index);
}
inline const ::SensitiveEngine::SensitiveDataInfo& SensitiveExtractPackage::sensitive_data_infos(int index) const {
  // @@protoc_insertion_point(field_get:SensitiveEngine.SensitiveExtractPackage.sensitive_data_infos)
  return _internal_sensitive_data_infos(index);
}
inline ::SensitiveEngine::SensitiveDataInfo* SensitiveExtractPackage::_internal_add_sensitive_data_infos() {
  return _impl_.sensitive_data_infos_.Add();
}
inline ::SensitiveEngine::SensitiveDataInfo* SensitiveExtractPackage::add_sensitive_data_infos() {
  ::SensitiveEngine::SensitiveDataInfo* _add = _internal_add_sensitive_data_infos();
  // @@protoc_insertion_point(field_add:SensitiveEngine.SensitiveExtractPackage.sensitive_data_infos)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::SensitiveEngine::SensitiveDataInfo >&
SensitiveExtractPackage::sensitive_data_infos() const {
  // @@protoc_insertion_point(field_list:SensitiveEngine.SensitiveExtractPackage.sensitive_data_infos)
  return _impl_.sensitive_data_infos_;
}

// bool file_type_suffix = 6;
inline void SensitiveExtractPackage::clear_file_type_suffix() {
  _impl_.file_type_suffix_ = false;
}
inline bool SensitiveExtractPackage::_internal_file_type_suffix() const {
  return _impl_.file_type_suffix_;
}
inline bool SensitiveExtractPackage::file_type_suffix() const {
  // @@protoc_insertion_point(field_get:SensitiveEngine.SensitiveExtractPackage.file_type_suffix)
  return _internal_file_type_suffix();
}
inline void SensitiveExtractPackage::_internal_set_file_type_suffix(bool value) {
  
  _impl_.file_type_suffix_ = value;
}
inline void SensitiveExtractPackage::set_file_type_suffix(bool value) {
  _internal_set_file_type_suffix(value);
  // @@protoc_insertion_point(field_set:SensitiveEngine.SensitiveExtractPackage.file_type_suffix)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace SensitiveEngine

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::SensitiveEngine::SensitiveExtractParam_MatchType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::SensitiveEngine::SensitiveExtractParam_MatchType>() {
  return ::SensitiveEngine::SensitiveExtractParam_MatchType_descriptor();
}
template <> struct is_proto_enum< ::SensitiveEngine::SensitiveExtractParam_MatchMode> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::SensitiveEngine::SensitiveExtractParam_MatchMode>() {
  return ::SensitiveEngine::SensitiveExtractParam_MatchMode_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_sensitive_5fengine_2eproto
